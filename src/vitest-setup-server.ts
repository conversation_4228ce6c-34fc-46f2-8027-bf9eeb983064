import { vi, beforeAll, afterEach } from 'vitest';
import { createSentryMock, resetSentryMocks } from './tests/mocks/sentry';

// Mock Sentry before any imports
vi.mock('@sentry/sveltekit', () => createSentryMock());

// Mock environment variables that <PERSON><PERSON> might use
vi.mock('$app/environment', () => ({
	dev: true,
	building: false,
	version: 'test',
}));

// Prevent Sentry from being initialized during tests
beforeAll(() => {
	// Set NODE_ENV to test to disable Sentry
	process.env.NODE_ENV = 'test';
	
	// Mock any global Sentry initialization
	if (typeof globalThis !== 'undefined') {
		globalThis.__SENTRY__ = undefined;
	}
});

// Reset Sentry mocks after each test
afterEach(() => {
	resetSentryMocks();
});
