lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@internationalized/date':
        specifier: ^3.8.2
        version: 3.8.2
      '@sentry/sveltekit':
        specifier: ^9.36.0
        version: 9.36.0(@sveltejs/kit@2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      '@supabase/ssr':
        specifier: ^0.6.1
        version: 0.6.1(@supabase/supabase-js@2.50.3)
      '@supabase/supabase-js':
        specifier: ^2.50.3
        version: 2.50.3
      d3-color:
        specifier: ^3.1.0
        version: 3.1.0
      d3-hierarchy:
        specifier: ^3.1.2
        version: 3.1.2
      d3-scale:
        specifier: ^4.0.2
        version: 4.0.2
      d3-scale-chromatic:
        specifier: ^3.1.0
        version: 3.1.0
      date-fns:
        specifier: ^4.1.0
        version: 4.1.0
      resend:
        specifier: ^4.6.0
        version: 4.6.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      uid:
        specifier: ^2.0.2
        version: 2.0.2
      xlsx:
        specifier: file:vendor/xlsx-0.20.3.tgz
        version: file:vendor/xlsx-0.20.3.tgz
    devDependencies:
      '@chromatic-com/storybook':
        specifier: ^4.0.1
        version: 4.0.1(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))
      '@eslint/compat':
        specifier: ^1.3.1
        version: 1.3.1(eslint@9.30.1(jiti@2.4.2))
      '@eslint/js':
        specifier: ^9.30.1
        version: 9.30.1
      '@lucide/svelte':
        specifier: ^0.525.0
        version: 0.525.0(svelte@5.35.4)
      '@playwright/test':
        specifier: ^1.53.2
        version: 1.53.2
      '@storybook/addon-docs':
        specifier: 9.1.0-alpha.2
        version: 9.1.0-alpha.2(@types/react@19.1.8)(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))
      '@storybook/addon-svelte-csf':
        specifier: 5.0.6
        version: 5.0.6(@storybook/svelte@9.1.0-alpha.2(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(svelte@5.35.4))(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      '@storybook/addon-vitest':
        specifier: 9.1.0-alpha.2
        version: 9.1.0-alpha.2(@vitest/browser@3.2.4)(@vitest/runner@3.2.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(vitest@3.2.4)
      '@storybook/sveltekit':
        specifier: 9.1.0-alpha.2
        version: 9.1.0-alpha.2(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      '@sveltejs/adapter-vercel':
        specifier: ^5.7.2
        version: 5.7.2(@sveltejs/kit@2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(rollup@4.44.2)
      '@sveltejs/kit':
        specifier: ^2.22.3
        version: 2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      '@sveltejs/vite-plugin-svelte':
        specifier: ^5.1.0
        version: 5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      '@tailwindcss/container-queries':
        specifier: ^0.1.1
        version: 0.1.1(tailwindcss@4.1.11)
      '@tailwindcss/forms':
        specifier: ^0.5.10
        version: 0.5.10(tailwindcss@4.1.11)
      '@tailwindcss/typography':
        specifier: ^0.5.16
        version: 0.5.16(tailwindcss@4.1.11)
      '@tailwindcss/vite':
        specifier: ^4.1.11
        version: 4.1.11(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      '@tanstack/table-core':
        specifier: ^8.21.3
        version: 8.21.3
      '@types/d3-color':
        specifier: ^3.1.3
        version: 3.1.3
      '@types/d3-hierarchy':
        specifier: ^3.1.7
        version: 3.1.7
      '@types/d3-scale':
        specifier: ^4.0.9
        version: 4.0.9
      '@types/d3-scale-chromatic':
        specifier: ^3.1.0
        version: 3.1.0
      '@vitest/browser':
        specifier: 3.2.4
        version: 3.2.4(playwright@1.53.2)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))(vitest@3.2.4)
      '@vitest/coverage-v8':
        specifier: 3.2.4
        version: 3.2.4(@vitest/browser@3.2.4)(vitest@3.2.4)
      bits-ui:
        specifier: ^2.8.10
        version: 2.8.10(@internationalized/date@3.8.2)(svelte@5.35.4)
      clsx:
        specifier: ^2.1.1
        version: 2.1.1
      eslint:
        specifier: ^9.30.1
        version: 9.30.1(jiti@2.4.2)
      eslint-config-prettier:
        specifier: ^10.1.5
        version: 10.1.5(eslint@9.30.1(jiti@2.4.2))
      eslint-plugin-storybook:
        specifier: 9.1.0-alpha.2
        version: 9.1.0-alpha.2(eslint@9.30.1(jiti@2.4.2))(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(typescript@5.8.3)
      eslint-plugin-svelte:
        specifier: ^3.10.1
        version: 3.10.1(eslint@9.30.1(jiti@2.4.2))(svelte@5.35.4)
      formsnap:
        specifier: ^2.0.1
        version: 2.0.1(svelte@5.35.4)(sveltekit-superforms@2.27.1(@sveltejs/kit@2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(@types/json-schema@7.0.15)(esbuild@0.25.6)(svelte@5.35.4)(typescript@5.8.3))
      globals:
        specifier: ^16.3.0
        version: 16.3.0
      layerchart:
        specifier: 2.0.0-next.6
        version: 2.0.0-next.6(svelte@5.35.4)
      mode-watcher:
        specifier: ^1.1.0
        version: 1.1.0(svelte@5.35.4)
      paneforge:
        specifier: 1.0.0
        version: 1.0.0(svelte@5.35.4)
      phosphor-svelte:
        specifier: ^3.0.1
        version: 3.0.1(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      playwright:
        specifier: ^1.53.2
        version: 1.53.2
      prettier:
        specifier: ^3.6.2
        version: 3.6.2
      prettier-plugin-sql:
        specifier: ^0.19.1
        version: 0.19.1(prettier@3.6.2)
      prettier-plugin-svelte:
        specifier: ^3.4.0
        version: 3.4.0(prettier@3.6.2)(svelte@5.35.4)
      prettier-plugin-tailwindcss:
        specifier: ^0.6.13
        version: 0.6.13(prettier-plugin-svelte@3.4.0(prettier@3.6.2)(svelte@5.35.4))(prettier@3.6.2)
      pretty-quick:
        specifier: ^4.2.2
        version: 4.2.2(prettier@3.6.2)
      simple-git-hooks:
        specifier: ^2.13.0
        version: 2.13.0
      storybook:
        specifier: 9.1.0-alpha.2
        version: 9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2)
      supabase:
        specifier: ^2.30.4
        version: 2.30.4
      svelte:
        specifier: ^5.35.4
        version: 5.35.4
      svelte-check:
        specifier: ^4.2.2
        version: 4.2.2(picomatch@4.0.2)(svelte@5.35.4)(typescript@5.8.3)
      svelte-sonner:
        specifier: ^1.0.5
        version: 1.0.5(svelte@5.35.4)
      sveltekit-flash-message:
        specifier: ^2.4.6
        version: 2.4.6(@sveltejs/kit@2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)
      sveltekit-superforms:
        specifier: ^2.27.1
        version: 2.27.1(@sveltejs/kit@2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(@types/json-schema@7.0.15)(esbuild@0.25.6)(svelte@5.35.4)(typescript@5.8.3)
      tailwind-merge:
        specifier: ^3.3.1
        version: 3.3.1
      tailwind-variants:
        specifier: ^1.0.0
        version: 1.0.0(tailwindcss@4.1.11)
      tailwindcss:
        specifier: ^4.1.11
        version: 4.1.11
      tw-animate-css:
        specifier: ^1.3.5
        version: 1.3.5
      typescript:
        specifier: ^5.8.3
        version: 5.8.3
      typescript-eslint:
        specifier: ^8.36.0
        version: 8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3)
      vite:
        specifier: ^6.3.5
        version: 6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)
      vitest:
        specifier: ^3.2.4
        version: 3.2.4(@types/node@24.0.12)(@vitest/browser@3.2.4)(jiti@2.4.2)(lightningcss@1.30.1)
      vitest-browser-svelte:
        specifier: ^1.0.0
        version: 1.0.0(@vitest/browser@3.2.4)(svelte@5.35.4)(vitest@3.2.4)
      zod:
        specifier: ^3.25.76
        version: 3.25.76

packages:

  '@adobe/css-tools@4.4.3':
    resolution: {integrity: sha512-VQKMkwriZbaOgVCby1UDY/LDk5fIjhQicCvVPFqfe+69fWaPWydbWJ3wRt59/YzIwda1I81loas3oCoHxnqvdA==}

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@ark/schema@0.46.0':
    resolution: {integrity: sha512-c2UQdKgP2eqqDArfBqQIJppxJHvNNXuQPeuSPlDML4rjw+f1cu0qAlzOG4b8ujgm9ctIDWwhpyw6gjG5ledIVQ==}

  '@ark/util@0.46.0':
    resolution: {integrity: sha512-JPy/NGWn/lvf1WmGCPw2VGpBg5utZraE84I7wli18EDF3p3zc/e9WolT35tINeZO3l7C77SjqRJeAUoT0CvMRg==}

  '@babel/code-frame@7.27.1':
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.28.0':
    resolution: {integrity: sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.28.0':
    resolution: {integrity: sha512-UlLAnTPrFdNGoFtbSXwcGFQBtQZJCNjaN6hQNP3UPvuNXT1i82N26KL3dZeIpNalWywr9IuQuncaAfUaS1g6sQ==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.28.0':
    resolution: {integrity: sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.2':
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-globals@7.28.0':
    resolution: {integrity: sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.27.1':
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.27.3':
    resolution: {integrity: sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.27.1':
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.6':
    resolution: {integrity: sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.26.9':
    resolution: {integrity: sha512-81NWa1njQblgZbQHxWHpxxCzNsa3ZwvFqpUg7P+NNUU6f3UU2jBEg4OlF/J6rl8+PQGh1q6/zWScd001YwcA5A==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/parser@7.28.0':
    resolution: {integrity: sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/runtime@7.27.6':
    resolution: {integrity: sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.27.2':
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.28.0':
    resolution: {integrity: sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.28.0':
    resolution: {integrity: sha512-jYnje+JyZG5YThjHiF28oT4SIZLnYOcSBb6+SDaFIyzDVSkXQmQQYclJ2R+YxcdmK0AX6x1E5OQNtuh3jHDrUg==}
    engines: {node: '>=6.9.0'}

  '@bcoe/v8-coverage@1.0.2':
    resolution: {integrity: sha512-6zABk/ECA/QYSCQ1NGiVwwbQerUCZ+TQbp64Q3AgmfNvurHH0j8TtXa1qbShXA6qqkpAj4V5W8pP6mLe1mcMqA==}
    engines: {node: '>=18'}

  '@chromatic-com/storybook@4.0.1':
    resolution: {integrity: sha512-GQXe5lyZl3yLewLJQyFXEpOp2h+mfN2bPrzYaOFNCJjO4Js9deKbRHTOSaiP2FRwZqDLdQwy2+SEGeXPZ94yYw==}
    engines: {node: '>=20.0.0', yarn: '>=1.22.18'}
    peerDependencies:
      storybook: ^0.0.0-0 || ^9.0.0 || ^9.1.0-0

  '@dagrejs/dagre@1.1.5':
    resolution: {integrity: sha512-Ghgrh08s12DCL5SeiR6AoyE80mQELTWhJBRmXfFoqDiFkR458vPEdgTbbjA0T+9ETNxUblnD0QW55tfdvi5pjQ==}

  '@dagrejs/graphlib@2.2.4':
    resolution: {integrity: sha512-mepCf/e9+SKYy1d02/UkvSy6+6MoyXhVxP8lLDfA7BPE1X1d4dR0sZznmbM8/XVJ1GPM+Svnx7Xj6ZweByWUkw==}
    engines: {node: '>17.0.0'}

  '@esbuild/aix-ppc64@0.25.6':
    resolution: {integrity: sha512-ShbM/3XxwuxjFiuVBHA+d3j5dyac0aEVVq1oluIDf71hUw0aRF59dV/efUsIwFnR6m8JNM2FjZOzmaZ8yG61kw==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.25.6':
    resolution: {integrity: sha512-hd5zdUarsK6strW+3Wxi5qWws+rJhCCbMiC9QZyzoxfk5uHRIE8T287giQxzVpEvCwuJ9Qjg6bEjcRJcgfLqoA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.25.6':
    resolution: {integrity: sha512-S8ToEOVfg++AU/bHwdksHNnyLyVM+eMVAOf6yRKFitnwnbwwPNqKr3srzFRe7nzV69RQKb5DgchIX5pt3L53xg==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.25.6':
    resolution: {integrity: sha512-0Z7KpHSr3VBIO9A/1wcT3NTy7EB4oNC4upJ5ye3R7taCc2GUdeynSLArnon5G8scPwaU866d3H4BCrE5xLW25A==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.25.6':
    resolution: {integrity: sha512-FFCssz3XBavjxcFxKsGy2DYK5VSvJqa6y5HXljKzhRZ87LvEi13brPrf/wdyl/BbpbMKJNOr1Sd0jtW4Ge1pAA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.6':
    resolution: {integrity: sha512-GfXs5kry/TkGM2vKqK2oyiLFygJRqKVhawu3+DOCk7OxLy/6jYkWXhlHwOoTb0WqGnWGAS7sooxbZowy+pK9Yg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.25.6':
    resolution: {integrity: sha512-aoLF2c3OvDn2XDTRvn8hN6DRzVVpDlj2B/F66clWd/FHLiHaG3aVZjxQX2DYphA5y/evbdGvC6Us13tvyt4pWg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.6':
    resolution: {integrity: sha512-2SkqTjTSo2dYi/jzFbU9Plt1vk0+nNg8YC8rOXXea+iA3hfNJWebKYPs3xnOUf9+ZWhKAaxnQNUf2X9LOpeiMQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.25.6':
    resolution: {integrity: sha512-b967hU0gqKd9Drsh/UuAm21Khpoh6mPBSgz8mKRq4P5mVK8bpA+hQzmm/ZwGVULSNBzKdZPQBRT3+WuVavcWsQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.25.6':
    resolution: {integrity: sha512-SZHQlzvqv4Du5PrKE2faN0qlbsaW/3QQfUUc6yO2EjFcA83xnwm91UbEEVx4ApZ9Z5oG8Bxz4qPE+HFwtVcfyw==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.25.6':
    resolution: {integrity: sha512-aHWdQ2AAltRkLPOsKdi3xv0mZ8fUGPdlKEjIEhxCPm5yKEThcUjHpWB1idN74lfXGnZ5SULQSgtr5Qos5B0bPw==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.25.6':
    resolution: {integrity: sha512-VgKCsHdXRSQ7E1+QXGdRPlQ/e08bN6WMQb27/TMfV+vPjjTImuT9PmLXupRlC90S1JeNNW5lzkAEO/McKeJ2yg==}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.6':
    resolution: {integrity: sha512-WViNlpivRKT9/py3kCmkHnn44GkGXVdXfdc4drNmRl15zVQ2+D2uFwdlGh6IuK5AAnGTo2qPB1Djppj+t78rzw==}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.6':
    resolution: {integrity: sha512-wyYKZ9NTdmAMb5730I38lBqVu6cKl4ZfYXIs31Baf8aoOtB4xSGi3THmDYt4BTFHk7/EcVixkOV2uZfwU3Q2Jw==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.6':
    resolution: {integrity: sha512-KZh7bAGGcrinEj4qzilJ4hqTY3Dg2U82c8bv+e1xqNqZCrCyc+TL9AUEn5WGKDzm3CfC5RODE/qc96OcbIe33w==}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.25.6':
    resolution: {integrity: sha512-9N1LsTwAuE9oj6lHMyyAM+ucxGiVnEqUdp4v7IaMmrwb06ZTEVCIs3oPPplVsnjPfyjmxwHxHMF8b6vzUVAUGw==}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.25.6':
    resolution: {integrity: sha512-A6bJB41b4lKFWRKNrWoP2LHsjVzNiaurf7wyj/XtFNTsnPuxwEBWHLty+ZE0dWBKuSK1fvKgrKaNjBS7qbFKig==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.25.6':
    resolution: {integrity: sha512-IjA+DcwoVpjEvyxZddDqBY+uJ2Snc6duLpjmkXm/v4xuS3H+3FkLZlDm9ZsAbF9rsfP3zeA0/ArNDORZgrxR/Q==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.6':
    resolution: {integrity: sha512-dUXuZr5WenIDlMHdMkvDc1FAu4xdWixTCRgP7RQLBOkkGgwuuzaGSYcOpW4jFxzpzL1ejb8yF620UxAqnBrR9g==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.25.6':
    resolution: {integrity: sha512-l8ZCvXP0tbTJ3iaqdNf3pjaOSd5ex/e6/omLIQCVBLmHTlfXW3zAxQ4fnDmPLOB1x9xrcSi/xtCWFwCZRIaEwg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.6':
    resolution: {integrity: sha512-hKrmDa0aOFOr71KQ/19JC7az1P0GWtCN1t2ahYAf4O007DHZt/dW8ym5+CUdJhQ/qkZmI1HAF8KkJbEFtCL7gw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/openharmony-arm64@0.25.6':
    resolution: {integrity: sha512-+SqBcAWoB1fYKmpWoQP4pGtx+pUUC//RNYhFdbcSA16617cchuryuhOCRpPsjCblKukAckWsV+aQ3UKT/RMPcA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openharmony]

  '@esbuild/sunos-x64@0.25.6':
    resolution: {integrity: sha512-dyCGxv1/Br7MiSC42qinGL8KkG4kX0pEsdb0+TKhmJZgCUDBGmyo1/ArCjNGiOLiIAgdbWgmWgib4HoCi5t7kA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.25.6':
    resolution: {integrity: sha512-42QOgcZeZOvXfsCBJF5Afw73t4veOId//XD3i+/9gSkhSV6Gk3VPlWncctI+JcOyERv85FUo7RxuxGy+z8A43Q==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.25.6':
    resolution: {integrity: sha512-4AWhgXmDuYN7rJI6ORB+uU9DHLq/erBbuMoAuB4VWJTu5KtCgcKYPynF0YI1VkBNuEfjNlLrFr9KZPJzrtLkrQ==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.25.6':
    resolution: {integrity: sha512-NgJPHHbEpLQgDH2MjQu90pzW/5vvXIZ7KOnPyNBm92A6WgZ/7b6fJyUBjoumLqeOQQGqY2QjQxRo97ah4Sj0cA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-utils@4.7.0':
    resolution: {integrity: sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/compat@1.3.1':
    resolution: {integrity: sha512-k8MHony59I5EPic6EQTCNOuPoVBnoYXkP+20xvwFjN7t0qI3ImyvyBgg+hIVPwC8JaxVjjUZld+cLfBLFDLucg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.40 || 9
    peerDependenciesMeta:
      eslint:
        optional: true

  '@eslint/config-array@0.21.0':
    resolution: {integrity: sha512-ENIdc4iLu0d93HeYirvKmrzshzofPw6VkZRKQGe9Nv46ZnWUzcF1xV01dcvEg/1wXUR61OmmlSfyeyO7EvjLxQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/config-helpers@0.3.0':
    resolution: {integrity: sha512-ViuymvFmcJi04qdZeDc2whTHryouGcDlaxPqarTD0ZE10ISpxGUVZGZDx4w01upyIynL3iu6IXH2bS1NhclQMw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.14.0':
    resolution: {integrity: sha512-qIbV0/JZr7iSDjqAc60IqbLdsj9GDt16xQtWD+B78d/HAlvysGdZZ6rpJHGAc2T0FQx1X6thsSPdnoiGKdNtdg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.15.1':
    resolution: {integrity: sha512-bkOp+iumZCCbt1K1CmWf0R9pM5yKpDv+ZXtvSyQpudrI9kuFLp+bM2WOPXImuD/ceQuaa8f5pj93Y7zyECIGNA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/eslintrc@3.3.1':
    resolution: {integrity: sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/js@9.30.1':
    resolution: {integrity: sha512-zXhuECFlyep42KZUhWjfvsmXGX39W8K8LFb8AWXM9gSV9dQB+MrJGLKvW6Zw0Ggnbpw0VHTtrhFXYe3Gym18jg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/object-schema@2.1.6':
    resolution: {integrity: sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.3.3':
    resolution: {integrity: sha512-1+WqvgNMhmlAambTvT3KPtCl/Ibr68VldY2XY40SL1CE0ZXiakFR/cbTspaF5HsnpDMvcYYoJHfl4980NBjGag==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@exodus/schemasafe@1.3.0':
    resolution: {integrity: sha512-5Aap/GaRupgNx/feGBwLLTVv8OQFfv3pq2lPRzPg9R+IOBnDgghTGW7l7EuVXOvg5cc/xSAlRW8rBrjIC3Nvqw==}

  '@floating-ui/core@1.7.2':
    resolution: {integrity: sha512-wNB5ooIKHQc+Kui96jE/n69rHFWAVoxn5CAzL1Xdd8FG03cgY3MLO+GF9U3W737fYDSgPWA6MReKhBQBop6Pcw==}

  '@floating-ui/dom@1.7.2':
    resolution: {integrity: sha512-7cfaOQuCS27HD7DX+6ib2OrnW+b4ZBwDNnCcT0uTyidcmyWb03FnQqJybDBoCnpdxwBSfA94UAYlRCt7mV+TbA==}

  '@floating-ui/utils@0.2.10':
    resolution: {integrity: sha512-aGTxbpbg8/b5JfU1HXSrbH3wXZuLPJcNEcZQFMxLs3oSzgtVu6nFPkbbGGUvBcUjKV2YyB9Wxxabo+HEH9tcRQ==}

  '@gcornut/valibot-json-schema@0.42.0':
    resolution: {integrity: sha512-4Et4AN6wmqeA0PfU5Clkv/IS27wiefsWf6TemAZrb75uzkClYEFavim7SboeKwbll9Nbsn2Iv0LT/HS5H7orZg==}
    hasBin: true

  '@hapi/hoek@9.3.0':
    resolution: {integrity: sha512-/c6rf4UJlmHlC9b5BaNvzAcFv7HZ2QHaV0D4/HNlBdvFnvQq8RI4kYdhyPCl7Xj+oWvTWQ8ujhqS53LIgAe6KQ==}

  '@hapi/topo@5.1.0':
    resolution: {integrity: sha512-foQZKJig7Ob0BMAYBfcJk8d77QtOe7Wo4ox7ff1lQYoNNAb6jwcY1ncdoy2e9wQZzvNy7ODZCYJkK8kzmcAnAg==}

  '@humanfs/core@0.19.1':
    resolution: {integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==}
    engines: {node: '>=18.18.0'}

  '@humanfs/node@0.16.6':
    resolution: {integrity: sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==}
    engines: {node: '>=18.18.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}

  '@humanwhocodes/retry@0.3.1':
    resolution: {integrity: sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==}
    engines: {node: '>=18.18'}

  '@humanwhocodes/retry@0.4.3':
    resolution: {integrity: sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ==}
    engines: {node: '>=18.18'}

  '@internationalized/date@3.8.2':
    resolution: {integrity: sha512-/wENk7CbvLbkUvX1tu0mwq49CVkkWpkXubGel6birjRPyo6uQ4nQpnq5xZu823zRCwwn82zgHrvgF1vZyvmVgA==}

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}

  '@isaacs/fs-minipass@4.0.1':
    resolution: {integrity: sha512-wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w==}
    engines: {node: '>=18.0.0'}

  '@istanbuljs/schema@0.1.3':
    resolution: {integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==}
    engines: {node: '>=8'}

  '@jridgewell/gen-mapping@0.3.12':
    resolution: {integrity: sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.4':
    resolution: {integrity: sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==}

  '@jridgewell/trace-mapping@0.3.29':
    resolution: {integrity: sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==}

  '@layerstack/svelte-actions@1.0.1-next.2':
    resolution: {integrity: sha512-s4iGrFIt+obdHP+Mzre+drCiZttqs5UYSw6CfQs1D2IeqTFkkbvRvYosh47flMi6ElN4psCrCcwn8KntClhMow==}

  '@layerstack/svelte-state@0.1.0-next.3':
    resolution: {integrity: sha512-SOmu/1RGuBfJ37THo4cbl8Ruiijrhju5+J+w+2/AV2GtUtho51peYI2Zt9rFIwS/L0OtvQwN3oSLcfIuWoxCiQ==}

  '@layerstack/svelte-stores@1.0.2-next.2':
    resolution: {integrity: sha512-YC1eEwtRAG+1ek+xdkfHCVoUPrhnVbJM+M4xTzoD4JuIf45GTUtwXop9MARYVX2G3ZlUUw1TdbjmHpfbzVLI9g==}

  '@layerstack/tailwind@2.0.0-next.4':
    resolution: {integrity: sha512-46QUWvMaYGlGmi17vfT2o6mV7/0ZaWi0qJ4vjPKrayBsxn1mVjqX0cbW7EXC6JnmMVYt5CZu0768rWCM4nGCzA==}

  '@layerstack/utils@1.1.0-next.2':
    resolution: {integrity: sha512-9Y4TOAE0iIVvnrf1mkft79rF/vTO84bZuc4hjhJB7XguFjy2f00pEfrDjF+IqleWImMFfRu31K2IMM6wRWEBXQ==}

  '@lucide/svelte@0.525.0':
    resolution: {integrity: sha512-dyUxkXzepagLUzL8jHQNdeH286nC66ClLACsg+Neu/bjkRJWPWMzkT+H0DKlE70QdkicGCfs1ZGmXCc351hmZA==}
    peerDependencies:
      svelte: ^5

  '@lukeed/csprng@1.1.0':
    resolution: {integrity: sha512-Z7C/xXCiGWsg0KuKsHTKJxbWhpI3Vs5GwLfOean7MGyVFGqdRgBbAjOCh6u4bbjPc/8MJ2pZmK/0DLdCbivLDA==}
    engines: {node: '>=8'}

  '@mapbox/node-pre-gyp@2.0.0':
    resolution: {integrity: sha512-llMXd39jtP0HpQLVI37Bf1m2ADlEb35GYSh1SDSLsBhR+5iCxiNGlT31yqbNtVHygHAtMy6dWFERpU2JgufhPg==}
    engines: {node: '>=18'}
    hasBin: true

  '@mdx-js/react@3.1.0':
    resolution: {integrity: sha512-QjHtSaoameoalGnKDT3FoIl4+9RwyTmo9ZJGBdLOks/YOiWHoRDI3PUwEzOE7kEmGcV3AFcp9K6dYu9rEuKLAQ==}
    peerDependencies:
      '@types/react': '>=16'
      react: '>=16'

  '@neoconfetti/react@1.0.0':
    resolution: {integrity: sha512-klcSooChXXOzIm+SE5IISIAn3bYzYfPjbX7D7HoqZL84oAfgREeSg5vSIaSFH+DaGzzvImTyWe1OyrJ67vik4A==}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@opentelemetry/api-logs@0.57.2':
    resolution: {integrity: sha512-uIX52NnTM0iBh84MShlpouI7UKqkZ7MrUszTmaypHBu4r7NofznSnQRfJ+uUeDtQDj6w8eFGg5KBLDAwAPz1+A==}
    engines: {node: '>=14'}

  '@opentelemetry/api@1.9.0':
    resolution: {integrity: sha512-3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg==}
    engines: {node: '>=8.0.0'}

  '@opentelemetry/context-async-hooks@1.30.1':
    resolution: {integrity: sha512-s5vvxXPVdjqS3kTLKMeBMvop9hbWkwzBpu+mUO2M7sZtlkyDJGwFe33wRKnbaYDo8ExRVBIIdwIGrqpxHuKttA==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/core@1.30.1':
    resolution: {integrity: sha512-OOCM2C/QIURhJMuKaekP3TRBxBKxG/TWWA0TL2J6nXUtDnuCtccy49LUJF8xPFXMX+0LMcxFpCo8M9cGY1W6rQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/instrumentation-amqplib@0.46.1':
    resolution: {integrity: sha512-AyXVnlCf/xV3K/rNumzKxZqsULyITJH6OVLiW6730JPRqWA7Zc9bvYoVNpN6iOpTU8CasH34SU/ksVJmObFibQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-connect@0.43.1':
    resolution: {integrity: sha512-ht7YGWQuV5BopMcw5Q2hXn3I8eG8TH0J/kc/GMcW4CuNTgiP6wCu44BOnucJWL3CmFWaRHI//vWyAhaC8BwePw==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-dataloader@0.16.1':
    resolution: {integrity: sha512-K/qU4CjnzOpNkkKO4DfCLSQshejRNAJtd4esgigo/50nxCB6XCyi1dhAblUHM9jG5dRm8eu0FB+t87nIo99LYQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-express@0.47.1':
    resolution: {integrity: sha512-QNXPTWteDclR2B4pDFpz0TNghgB33UMjUt14B+BZPmtH1MwUFAfLHBaP5If0Z5NZC+jaH8oF2glgYjrmhZWmSw==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-fs@0.19.1':
    resolution: {integrity: sha512-6g0FhB3B9UobAR60BGTcXg4IHZ6aaYJzp0Ki5FhnxyAPt8Ns+9SSvgcrnsN2eGmk3RWG5vYycUGOEApycQL24A==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-generic-pool@0.43.1':
    resolution: {integrity: sha512-M6qGYsp1cURtvVLGDrPPZemMFEbuMmCXgQYTReC/IbimV5sGrLBjB+/hANUpRZjX67nGLdKSVLZuQQAiNz+sww==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-graphql@0.47.1':
    resolution: {integrity: sha512-EGQRWMGqwiuVma8ZLAZnExQ7sBvbOx0N/AE/nlafISPs8S+QtXX+Viy6dcQwVWwYHQPAcuY3bFt3xgoAwb4ZNQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-hapi@0.45.2':
    resolution: {integrity: sha512-7Ehow/7Wp3aoyCrZwQpU7a2CnoMq0XhIcioFuKjBb0PLYfBfmTsFTUyatlHu0fRxhwcRsSQRTvEhmZu8CppBpQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-http@0.57.2':
    resolution: {integrity: sha512-1Uz5iJ9ZAlFOiPuwYg29Bf7bJJc/GeoeJIFKJYQf67nTVKFe8RHbEtxgkOmK4UGZNHKXcpW4P8cWBYzBn1USpg==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-ioredis@0.47.1':
    resolution: {integrity: sha512-OtFGSN+kgk/aoKgdkKQnBsQFDiG8WdCxu+UrHr0bXScdAmtSzLSraLo7wFIb25RVHfRWvzI5kZomqJYEg/l1iA==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-kafkajs@0.7.1':
    resolution: {integrity: sha512-OtjaKs8H7oysfErajdYr1yuWSjMAectT7Dwr+axIoZqT9lmEOkD/H/3rgAs8h/NIuEi2imSXD+vL4MZtOuJfqQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-knex@0.44.1':
    resolution: {integrity: sha512-U4dQxkNhvPexffjEmGwCq68FuftFK15JgUF05y/HlK3M6W/G2iEaACIfXdSnwVNe9Qh0sPfw8LbOPxrWzGWGMQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-koa@0.47.1':
    resolution: {integrity: sha512-l/c+Z9F86cOiPJUllUCt09v+kICKvT+Vg1vOAJHtHPsJIzurGayucfCMq2acd/A/yxeNWunl9d9eqZ0G+XiI6A==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-lru-memoizer@0.44.1':
    resolution: {integrity: sha512-5MPkYCvG2yw7WONEjYj5lr5JFehTobW7wX+ZUFy81oF2lr9IPfZk9qO+FTaM0bGEiymwfLwKe6jE15nHn1nmHg==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-mongodb@0.52.0':
    resolution: {integrity: sha512-1xmAqOtRUQGR7QfJFfGV/M2kC7wmI2WgZdpru8hJl3S0r4hW0n3OQpEHlSGXJAaNFyvT+ilnwkT+g5L4ljHR6g==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-mongoose@0.46.1':
    resolution: {integrity: sha512-3kINtW1LUTPkiXFRSSBmva1SXzS/72we/jL22N+BnF3DFcoewkdkHPYOIdAAk9gSicJ4d5Ojtt1/HeibEc5OQg==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-mysql2@0.45.2':
    resolution: {integrity: sha512-h6Ad60FjCYdJZ5DTz1Lk2VmQsShiViKe0G7sYikb0GHI0NVvApp2XQNRHNjEMz87roFttGPLHOYVPlfy+yVIhQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-mysql@0.45.1':
    resolution: {integrity: sha512-TKp4hQ8iKQsY7vnp/j0yJJ4ZsP109Ht6l4RHTj0lNEG1TfgTrIH5vJMbgmoYXWzNHAqBH2e7fncN12p3BP8LFg==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-pg@0.51.1':
    resolution: {integrity: sha512-QxgjSrxyWZc7Vk+qGSfsejPVFL1AgAJdSBMYZdDUbwg730D09ub3PXScB9d04vIqPriZ+0dqzjmQx0yWKiCi2Q==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-redis-4@0.46.1':
    resolution: {integrity: sha512-UMqleEoabYMsWoTkqyt9WAzXwZ4BlFZHO40wr3d5ZvtjKCHlD4YXLm+6OLCeIi/HkX7EXvQaz8gtAwkwwSEvcQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-tedious@0.18.1':
    resolution: {integrity: sha512-5Cuy/nj0HBaH+ZJ4leuD7RjgvA844aY2WW+B5uLcWtxGjRZl3MNLuxnNg5DYWZNPO+NafSSnra0q49KWAHsKBg==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-undici@0.10.1':
    resolution: {integrity: sha512-rkOGikPEyRpMCmNu9AQuV5dtRlDmJp2dK5sw8roVshAGoB6hH/3QjDtRhdwd75SsJwgynWUNRUYe0wAkTo16tQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.7.0

  '@opentelemetry/instrumentation@0.57.2':
    resolution: {integrity: sha512-BdBGhQBh8IjZ2oIIX6F2/Q3LKm/FDDKi6ccYKcBTeilh6SNdNKveDOLk73BkSJjQLJk6qe4Yh+hHw1UPhCDdrg==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/redis-common@0.36.2':
    resolution: {integrity: sha512-faYX1N0gpLhej/6nyp6bgRjzAKXn5GOEMYY7YhciSfCoITAktLUtQ36d24QEWNA1/WA1y6qQunCe0OhHRkVl9g==}
    engines: {node: '>=14'}

  '@opentelemetry/resources@1.30.1':
    resolution: {integrity: sha512-5UxZqiAgLYGFjS4s9qm5mBVo433u+dSPUFWVWXmLAD4wB65oMCoXaJP1KJa9DIYYMeHu3z4BZcStG3LC593cWA==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/sdk-trace-base@1.30.1':
    resolution: {integrity: sha512-jVPgBbH1gCy2Lb7X0AVQ8XAfgg0pJ4nvl8/IiQA6nxOsPvS+0zMJaFSs2ltXe0J6C8dqjcnpyqINDJmU30+uOg==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/semantic-conventions@1.28.0':
    resolution: {integrity: sha512-lp4qAiMTD4sNWW4DbKLBkfiMZ4jbAboJIGOQr5DvciMRI494OapieI9qiODpOt0XBr1LjIDy1xAGAnVs5supTA==}
    engines: {node: '>=14'}

  '@opentelemetry/semantic-conventions@1.34.0':
    resolution: {integrity: sha512-aKcOkyrorBGlajjRdVoJWHTxfxO1vCNHLJVlSDaRHDIdjU+pX8IYQPvPDkYiujKLbRnWU+1TBwEt0QRgSm4SGA==}
    engines: {node: '>=14'}

  '@opentelemetry/sql-common@0.40.1':
    resolution: {integrity: sha512-nSDlnHSqzC3pXn/wZEZVLuAuJ1MYMXPBwtv2qAbCa3847SaHItdE7SzUq/Jtb0KZmh1zfAbNi3AAMjztTT4Ugg==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': ^1.1.0

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}

  '@pkgr/core@0.2.7':
    resolution: {integrity: sha512-YLT9Zo3oNPJoBjBc4q8G2mjU4tqIbf5CEOORbUUr48dCD9q3umJ3IPlVqOqDakPfd2HuwccBaqlGhN4Gmr5OWg==}
    engines: {node: ^12.20.0 || ^14.18.0 || >=16.0.0}

  '@playwright/test@1.53.2':
    resolution: {integrity: sha512-tEB2U5z74ebBeyfGNZ3Jfg29AnW+5HlWhvHtb/Mqco9pFdZU1ZLNdVb2UtB5CvmiilNr2ZfVH/qMmAROG/XTzw==}
    engines: {node: '>=18'}
    hasBin: true

  '@polka/url@1.0.0-next.29':
    resolution: {integrity: sha512-wwQAWhWSuHaag8c4q/KN/vCoeOJYshAIvMQwD4GpSb3OiZklFfvAgmj0VCBBImRpuF/aFgIRzllXlVX93Jevww==}

  '@poppinss/macroable@1.0.5':
    resolution: {integrity: sha512-6u61y1HHd090MEk1Av0/1btDmm2Hh/+XoJj+HgFYRh9koUPI822ybJbwLHuqjLNCiY+o1gRykg2igEqOf/VBZw==}

  '@prisma/instrumentation@6.11.1':
    resolution: {integrity: sha512-mrZOev24EDhnefmnZX7WVVT7v+r9LttPRqf54ONvj6re4XMF7wFTpK2tLJi4XHB7fFp/6xhYbgRel8YV7gQiyA==}
    peerDependencies:
      '@opentelemetry/api': ^1.8

  '@react-email/render@1.1.2':
    resolution: {integrity: sha512-RnRehYN3v9gVlNMehHPHhyp2RQo7+pSkHDtXPvg3s0GbzM9SQMW4Qrf8GRNvtpLC4gsI+Wt0VatNRUFqjvevbw==}
    engines: {node: '>=18.0.0'}
    peerDependencies:
      react: ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^18.0 || ^19.0 || ^19.0.0-rc

  '@rollup/pluginutils@5.2.0':
    resolution: {integrity: sha512-qWJ2ZTbmumwiLFomfzTyt5Kng4hwPi9rwCYN4SHb6eaRU1KNO4ccxINHr/VhH4GgPlt1XfSTLX2LBTme8ne4Zw==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/rollup-android-arm-eabi@4.44.2':
    resolution: {integrity: sha512-g0dF8P1e2QYPOj1gu7s/3LVP6kze9A7m6x0BZ9iTdXK8N5c2V7cpBKHV3/9A4Zd8xxavdhK0t4PnqjkqVmUc9Q==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.44.2':
    resolution: {integrity: sha512-Yt5MKrOosSbSaAK5Y4J+vSiID57sOvpBNBR6K7xAaQvk3MkcNVV0f9fE20T+41WYN8hDn6SGFlFrKudtx4EoxA==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.44.2':
    resolution: {integrity: sha512-EsnFot9ZieM35YNA26nhbLTJBHD0jTwWpPwmRVDzjylQT6gkar+zenfb8mHxWpRrbn+WytRRjE0WKsfaxBkVUA==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.44.2':
    resolution: {integrity: sha512-dv/t1t1RkCvJdWWxQ2lWOO+b7cMsVw5YFaS04oHpZRWehI1h0fV1gF4wgGCTyQHHjJDfbNpwOi6PXEafRBBezw==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.44.2':
    resolution: {integrity: sha512-W4tt4BLorKND4qeHElxDoim0+BsprFTwb+vriVQnFFtT/P6v/xO5I99xvYnVzKWrK6j7Hb0yp3x7V5LUbaeOMg==}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.44.2':
    resolution: {integrity: sha512-tdT1PHopokkuBVyHjvYehnIe20fxibxFCEhQP/96MDSOcyjM/shlTkZZLOufV3qO6/FQOSiJTBebhVc12JyPTA==}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.44.2':
    resolution: {integrity: sha512-+xmiDGGaSfIIOXMzkhJ++Oa0Gwvl9oXUeIiwarsdRXSe27HUIvjbSIpPxvnNsRebsNdUo7uAiQVgBD1hVriwSQ==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm-musleabihf@4.44.2':
    resolution: {integrity: sha512-bDHvhzOfORk3wt8yxIra8N4k/N0MnKInCW5OGZaeDYa/hMrdPaJzo7CSkjKZqX4JFUWjUGm88lI6QJLCM7lDrA==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm64-gnu@4.44.2':
    resolution: {integrity: sha512-NMsDEsDiYghTbeZWEGnNi4F0hSbGnsuOG+VnNvxkKg0IGDvFh7UVpM/14mnMwxRxUf9AdAVJgHPvKXf6FpMB7A==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-arm64-musl@4.44.2':
    resolution: {integrity: sha512-lb5bxXnxXglVq+7imxykIp5xMq+idehfl+wOgiiix0191av84OqbjUED+PRC5OA8eFJYj5xAGcpAZ0pF2MnW+A==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-loongarch64-gnu@4.44.2':
    resolution: {integrity: sha512-Yl5Rdpf9pIc4GW1PmkUGHdMtbx0fBLE1//SxDmuf3X0dUC57+zMepow2LK0V21661cjXdTn8hO2tXDdAWAqE5g==}
    cpu: [loong64]
    os: [linux]

  '@rollup/rollup-linux-powerpc64le-gnu@4.44.2':
    resolution: {integrity: sha512-03vUDH+w55s680YYryyr78jsO1RWU9ocRMaeV2vMniJJW/6HhoTBwyyiiTPVHNWLnhsnwcQ0oH3S9JSBEKuyqw==}
    cpu: [ppc64]
    os: [linux]

  '@rollup/rollup-linux-riscv64-gnu@4.44.2':
    resolution: {integrity: sha512-iYtAqBg5eEMG4dEfVlkqo05xMOk6y/JXIToRca2bAWuqjrJYJlx/I7+Z+4hSrsWU8GdJDFPL4ktV3dy4yBSrzg==}
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-riscv64-musl@4.44.2':
    resolution: {integrity: sha512-e6vEbgaaqz2yEHqtkPXa28fFuBGmUJ0N2dOJK8YUfijejInt9gfCSA7YDdJ4nYlv67JfP3+PSWFX4IVw/xRIPg==}
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-s390x-gnu@4.44.2':
    resolution: {integrity: sha512-evFOtkmVdY3udE+0QKrV5wBx7bKI0iHz5yEVx5WqDJkxp9YQefy4Mpx3RajIVcM6o7jxTvVd/qpC1IXUhGc1Mw==}
    cpu: [s390x]
    os: [linux]

  '@rollup/rollup-linux-x64-gnu@4.44.2':
    resolution: {integrity: sha512-/bXb0bEsWMyEkIsUL2Yt5nFB5naLAwyOWMEviQfQY1x3l5WsLKgvZf66TM7UTfED6erckUVUJQ/jJ1FSpm3pRQ==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-linux-x64-musl@4.44.2':
    resolution: {integrity: sha512-3D3OB1vSSBXmkGEZR27uiMRNiwN08/RVAcBKwhUYPaiZ8bcvdeEwWPvbnXvvXHY+A/7xluzcN+kaiOFNiOZwWg==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-win32-arm64-msvc@4.44.2':
    resolution: {integrity: sha512-VfU0fsMK+rwdK8mwODqYeM2hDrF2WiHaSmCBrS7gColkQft95/8tphyzv2EupVxn3iE0FI78wzffoULH1G+dkw==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.44.2':
    resolution: {integrity: sha512-+qMUrkbUurpE6DVRjiJCNGZBGo9xM4Y0FXU5cjgudWqIBWbcLkjE3XprJUsOFgC6xjBClwVa9k6O3A7K3vxb5Q==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.44.2':
    resolution: {integrity: sha512-3+QZROYfJ25PDcxFF66UEk8jGWigHJeecZILvkPkyQN7oc5BvFo4YEXFkOs154j3FTMp9mn9Ky8RCOwastduEA==}
    cpu: [x64]
    os: [win32]

  '@selderee/plugin-htmlparser2@0.11.0':
    resolution: {integrity: sha512-P33hHGdldxGabLFjPPpaTxVolMrzrcegejx+0GxjrIb9Zv48D8yAIA/QTDR2dFl7Uz7urX8aX6+5bCZslr+gWQ==}

  '@sentry-internal/browser-utils@9.36.0':
    resolution: {integrity: sha512-4cGT7c4kr4DMRlcnErgWiL/uLwHvD8A52w6ffJkT/Bj7olSUjFCo8RKIEsDiJqk8No3yfoS6dRbudMZUIFuDTA==}
    engines: {node: '>=18'}

  '@sentry-internal/feedback@9.36.0':
    resolution: {integrity: sha512-0FNKlP2/CWbitAqtLKVPuSqMs8h7jMJm812wSA0gPpCBVMoX+mWEKQh3pYHYekHVfI0muieX9CWYlup0McXaJA==}
    engines: {node: '>=18'}

  '@sentry-internal/replay-canvas@9.36.0':
    resolution: {integrity: sha512-/RrYKvQ80gzjEJoHWMFXqYPlQS8ovEUWG2KeSJQdFtvL+/M/864jfxTA21hgHJC/5GbnOq4V541C1P5YVXwg6w==}
    engines: {node: '>=18'}

  '@sentry-internal/replay@9.36.0':
    resolution: {integrity: sha512-BKxDz4r7bC23d9+zx3a0qkjWLa4zgE/8tXSfcSVYwhczEzTNXYOPxIodYm2IzgVgN9NwUE1m9Cf+/xlKKQLyFQ==}
    engines: {node: '>=18'}

  '@sentry/babel-plugin-component-annotate@3.5.0':
    resolution: {integrity: sha512-s2go8w03CDHbF9luFGtBHKJp4cSpsQzNVqgIa9Pfa4wnjipvrK6CxVT4icpLA3YO6kg5u622Yoa5GF3cJdippw==}
    engines: {node: '>= 14'}

  '@sentry/browser@9.36.0':
    resolution: {integrity: sha512-DCn6VoJCWMjZm5sOfD2+2EgEVdr+H/8Hqs080ruWZo1MZzIUmF1+qnG7AtYeIlm93OydU0PLjvYeWo0zttgGvg==}
    engines: {node: '>=18'}

  '@sentry/bundler-plugin-core@3.5.0':
    resolution: {integrity: sha512-zDzPrhJqAAy2VzV4g540qAZH4qxzisstK2+NIJPZUUKztWRWUV2cMHsyUtdctYgloGkLyGpZJBE3RE6dmP/xqQ==}
    engines: {node: '>= 14'}

  '@sentry/cli-darwin@2.42.2':
    resolution: {integrity: sha512-GtJSuxER7Vrp1IpxdUyRZzcckzMnb4N5KTW7sbTwUiwqARRo+wxS+gczYrS8tdgtmXs5XYhzhs+t4d52ITHMIg==}
    engines: {node: '>=10'}
    os: [darwin]

  '@sentry/cli-linux-arm64@2.42.2':
    resolution: {integrity: sha512-BOxzI7sgEU5Dhq3o4SblFXdE9zScpz6EXc5Zwr1UDZvzgXZGosUtKVc7d1LmkrHP8Q2o18HcDWtF3WvJRb5Zpw==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux, freebsd]

  '@sentry/cli-linux-arm@2.42.2':
    resolution: {integrity: sha512-7udCw+YL9lwq+9eL3WLspvnuG+k5Icg92YE7zsteTzWLwgPVzaxeZD2f8hwhsu+wmL+jNqbpCRmktPteh3i2mg==}
    engines: {node: '>=10'}
    cpu: [arm]
    os: [linux, freebsd]

  '@sentry/cli-linux-i686@2.42.2':
    resolution: {integrity: sha512-Sw/dQp5ZPvKnq3/y7wIJyxTUJYPGoTX/YeMbDs8BzDlu9to2LWV3K3r7hE7W1Lpbaw4tSquUHiQjP5QHCOS7aQ==}
    engines: {node: '>=10'}
    cpu: [x86, ia32]
    os: [linux, freebsd]

  '@sentry/cli-linux-x64@2.42.2':
    resolution: {integrity: sha512-mU4zUspAal6TIwlNLBV5oq6yYqiENnCWSxtSQVzWs0Jyq97wtqGNG9U+QrnwjJZ+ta/hvye9fvL2X25D/RxHQw==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux, freebsd]

  '@sentry/cli-win32-i686@2.42.2':
    resolution: {integrity: sha512-iHvFHPGqgJMNqXJoQpqttfsv2GI3cGodeTq4aoVLU/BT3+hXzbV0x1VpvvEhncJkDgDicJpFLM8sEPHb3b8abw==}
    engines: {node: '>=10'}
    cpu: [x86, ia32]
    os: [win32]

  '@sentry/cli-win32-x64@2.42.2':
    resolution: {integrity: sha512-vPPGHjYoaGmfrU7xhfFxG7qlTBacroz5NdT+0FmDn6692D8IvpNXl1K+eV3Kag44ipJBBeR8g1HRJyx/F/9ACw==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [win32]

  '@sentry/cli@2.42.2':
    resolution: {integrity: sha512-spb7S/RUumCGyiSTg8DlrCX4bivCNmU/A1hcfkwuciTFGu8l5CDc2I6jJWWZw8/0enDGxuj5XujgXvU5tr4bxg==}
    engines: {node: '>= 10'}
    hasBin: true

  '@sentry/cloudflare@9.36.0':
    resolution: {integrity: sha512-MDr2NyQ5x4V4XUL6LcPjHMYQxdPjTXqY2fn8G6XgS6FVsqWNeRuqikLhqWm6PzxEJTWaabUgEyOS8HDymm69OQ==}
    engines: {node: '>=18'}
    peerDependencies:
      '@cloudflare/workers-types': ^4.x
    peerDependenciesMeta:
      '@cloudflare/workers-types':
        optional: true

  '@sentry/core@9.36.0':
    resolution: {integrity: sha512-LU6EmsXPxi8QFkrx0fCqhXicsJA6uUWCD0VrxePZzs+Xs0SgVNDxOgRELVrZa4LPomQJBR5wmm3Duozp9JkHcQ==}
    engines: {node: '>=18'}

  '@sentry/node-core@9.36.0':
    resolution: {integrity: sha512-hrsIxkEbPHJ6a37LUFX4Bkbhx8nAKQVRCUDzwUw2TCXyW9eLPkbiIxW7Q4vNYAE7MiBTQoG7KJrb+0VMaNNS9A==}
    engines: {node: '>=18'}
    peerDependencies:
      '@opentelemetry/api': ^1.9.0
      '@opentelemetry/context-async-hooks': ^1.30.1 || ^2.0.0
      '@opentelemetry/core': ^1.30.1 || ^2.0.0
      '@opentelemetry/instrumentation': ^0.57.1 || ^0.202.0
      '@opentelemetry/resources': ^1.30.1 || ^2.0.0
      '@opentelemetry/sdk-trace-base': ^1.30.1 || ^2.0.0
      '@opentelemetry/semantic-conventions': ^1.34.0

  '@sentry/node@9.36.0':
    resolution: {integrity: sha512-MUaMWeuIm83LCu/1u9UmULqbhqEfqMjKHGNAZK/lqPu6Vh3E00AQhZmhRcVe7K8ZUOYuFQkSS40zNgKlcSz/hA==}
    engines: {node: '>=18'}

  '@sentry/opentelemetry@9.36.0':
    resolution: {integrity: sha512-1J3deP+QKekKv03EUj3GZRN8o3l+4o5wnAzaa1/5KqW7y+16eywYsIQsb6Mj7cGZ3Mjcg9ltSm5iDEpiqnuS4Q==}
    engines: {node: '>=18'}
    peerDependencies:
      '@opentelemetry/api': ^1.9.0
      '@opentelemetry/context-async-hooks': ^1.30.1 || ^2.0.0
      '@opentelemetry/core': ^1.30.1 || ^2.0.0
      '@opentelemetry/instrumentation': ^0.57.1 || ^0.202.0
      '@opentelemetry/sdk-trace-base': ^1.30.1 || ^2.0.0
      '@opentelemetry/semantic-conventions': ^1.34.0

  '@sentry/svelte@9.36.0':
    resolution: {integrity: sha512-iGNCfb8HnF+cngWZpB3GGD1QxJR5wzlgVGADKS4cunH7xJIKhSNo6Q6xFcCf2vnVfpu1xi+jfJNbQcTjv8d01g==}
    engines: {node: '>=18'}
    peerDependencies:
      svelte: 3.x || 4.x || 5.x

  '@sentry/sveltekit@9.36.0':
    resolution: {integrity: sha512-tas81aQ46TVfqAfvTtwADZGFsygdtb7g5Zw+iF0m0hwyPj1EMfKGG33njG50T5WNZ0DFCeae+UTUxAjUF9SWqQ==}
    engines: {node: '>=18'}
    peerDependencies:
      '@sveltejs/kit': 2.x
      vite: '*'
    peerDependenciesMeta:
      vite:
        optional: true

  '@sentry/vite-plugin@3.5.0':
    resolution: {integrity: sha512-jUnpTdpicG8wefamw7eNo2uO+Q3KCbOAiF76xH4gfNHSW6TN2hBfOtmLu7J+ive4c0Al3+NEHz19bIPR0lkwWg==}
    engines: {node: '>= 14'}

  '@sideway/address@4.1.5':
    resolution: {integrity: sha512-IqO/DUQHUkPeixNQ8n0JA6102hT9CmaljNTPmQ1u8MEhBo/R4Q8eKLN/vGZxuebwOroDB4cbpjheD4+/sKFK4Q==}

  '@sideway/formula@3.0.1':
    resolution: {integrity: sha512-/poHZJJVjx3L+zVD6g9KgHfYnb443oi7wLu/XKojDviHy6HOEOA6z1Trk5aR1dGcmPenJEgb2sK2I80LeS3MIg==}

  '@sideway/pinpoint@2.0.0':
    resolution: {integrity: sha512-RNiOoTPkptFtSVzQevY/yWtZwf/RxyVnPy/OcA9HBM3MlGDnBEYL5B41H0MTn0Uec8Hi+2qUtTfG2WWZBmMejQ==}

  '@sinclair/typebox@0.34.37':
    resolution: {integrity: sha512-2TRuQVgQYfy+EzHRTIvkhv2ADEouJ2xNS/Vq+W5EuuewBdOrvATvljZTxHWZSTYr2sTjTHpGvucaGAt67S2akw==}

  '@standard-schema/spec@1.0.0':
    resolution: {integrity: sha512-m2bOd0f2RT9k8QJx1JN85cZYyH1RqFBdlwtkSlf4tBDYLCiiZnv1fIIwacK6cqwXavOydf0NPToMQgpKq+dVlA==}

  '@storybook/addon-docs@9.1.0-alpha.2':
    resolution: {integrity: sha512-kk0onlSEV25m5c2/FEpVgehjtJzJNK1+pQMkI950j0mYkWqoR8TBSVO8MYme8c+Q0zD/8EfZfV1zp+PWMW4f0A==}
    peerDependencies:
      storybook: ^9.1.0-alpha.2

  '@storybook/addon-svelte-csf@5.0.6':
    resolution: {integrity: sha512-Ryo28eV7YrptIt9PmpIpDHAGXAKwfNKMvN/d79Bv7kYhdQ+LvyJkNzNOn6Tpem4soUZTsf4+CdqbKs3eHHIXPg==}
    peerDependencies:
      '@storybook/svelte': ^0.0.0-0 || ^8.2.0 || ^9.0.0 || ^9.1.0-0
      '@sveltejs/vite-plugin-svelte': ^4.0.0 || ^5.0.0
      storybook: ^0.0.0-0 || ^8.2.0 || ^9.0.0 || ^9.1.0-0
      svelte: ^5.0.0
      vite: ^5.0.0 || ^6.0.0

  '@storybook/addon-vitest@9.1.0-alpha.2':
    resolution: {integrity: sha512-aqTiQHrqSrVTzF3h9r84f0E3YCuIktCaEYuEOBSB0IMuvrinosLKXjrvAhXocQK91tjf8tXWR8XMO1H+BzFQ2g==}
    peerDependencies:
      '@vitest/browser': ^3.0.0
      '@vitest/runner': ^3.0.0
      storybook: ^9.1.0-alpha.2
      vitest: ^3.0.0
    peerDependenciesMeta:
      '@vitest/browser':
        optional: true
      '@vitest/runner':
        optional: true
      vitest:
        optional: true

  '@storybook/builder-vite@9.1.0-alpha.2':
    resolution: {integrity: sha512-N4RdEjLptun71RH/h4vQA551SKRtr+gyQGx5sVCyamdE52QRPnDOyoM5Kj1IOqTtqLPjYV4D7c0WV2mDIDYazg==}
    peerDependencies:
      storybook: ^9.1.0-alpha.2
      vite: ^5.0.0 || ^6.0.0

  '@storybook/csf-plugin@9.1.0-alpha.2':
    resolution: {integrity: sha512-h2zb2J9lst1QMphSDpv3exNyw8fq4w8krV0aNJ+42E/GfHs11jI2zO4aamMCacWl0wSHSDeJaVxFUgXbZbLGcA==}
    peerDependencies:
      storybook: ^9.1.0-alpha.2

  '@storybook/csf@0.1.13':
    resolution: {integrity: sha512-7xOOwCLGB3ebM87eemep89MYRFTko+D8qE7EdAAq74lgdqRR5cOUtYWJLjO2dLtP94nqoOdHJo6MdLLKzg412Q==}

  '@storybook/global@5.0.0':
    resolution: {integrity: sha512-FcOqPAXACP0I3oJ/ws6/rrPT9WGhu915Cg8D02a9YxLo0DE9zI+a9A5gRGvmQ09fiWPukqI8ZAEoQEdWUKMQdQ==}

  '@storybook/icons@1.4.0':
    resolution: {integrity: sha512-Td73IeJxOyalzvjQL+JXx72jlIYHgs+REaHiREOqfpo3A2AYYG71AUbcv+lg7mEDIweKVCxsMQ0UKo634c8XeA==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0-beta
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0-beta

  '@storybook/react-dom-shim@9.1.0-alpha.2':
    resolution: {integrity: sha512-u6JLCD06jkNkMm04PK5JtiaImHwqY6RSak5q/Z7cngAHm2T7Sg2pByREZvIZXYXzWpP7/m6BVWC5nCntBDAgKA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0-beta
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0-beta
      storybook: ^9.1.0-alpha.2

  '@storybook/svelte-vite@9.1.0-alpha.2':
    resolution: {integrity: sha512-ZelC0lgzJWvFOiduB32haQGp/bNHyE28uRO8omC5Kx4DbpdtOIxZKItKtpj/f1EZYTZnwE5BdZuKIAC0L1iqGw==}
    engines: {node: '>=20.0.0'}
    peerDependencies:
      '@sveltejs/vite-plugin-svelte': ^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0
      storybook: ^9.1.0-alpha.2
      svelte: ^5.0.0
      vite: ^5.0.0 || ^6.0.0

  '@storybook/svelte@9.1.0-alpha.2':
    resolution: {integrity: sha512-MuEmpLmpt/Z8YQazpIPyu1AiS/vCKmgyky4gIQG7CqO9mUL7USrOJvhozeLPswITdLV2YVFYrTg9Zn2FAI/Tcw==}
    engines: {node: '>=20.0.0'}
    peerDependencies:
      storybook: ^9.1.0-alpha.2
      svelte: ^5.0.0

  '@storybook/sveltekit@9.1.0-alpha.2':
    resolution: {integrity: sha512-qIr9y5AV/Y4ATfHTxRQb8nwlOdJaiIO8AKYnH+FU6kCRovt9EqQ3VIIXxKKAVBlrRWW7Aw3GDpqQo+DI5UYjWg==}
    engines: {node: '>=20.0.0'}
    peerDependencies:
      storybook: ^9.1.0-alpha.2
      svelte: ^5.0.0
      vite: ^5.0.0 || ^6.0.0

  '@supabase/auth-js@2.70.0':
    resolution: {integrity: sha512-BaAK/tOAZFJtzF1sE3gJ2FwTjLf4ky3PSvcvLGEgEmO4BSBkwWKu8l67rLLIBZPDnCyV7Owk2uPyKHa0kj5QGg==}

  '@supabase/functions-js@2.4.5':
    resolution: {integrity: sha512-v5GSqb9zbosquTo6gBwIiq7W9eQ7rE5QazsK/ezNiQXdCbY+bH8D9qEaBIkhVvX4ZRW5rP03gEfw5yw9tiq4EQ==}

  '@supabase/node-fetch@2.6.15':
    resolution: {integrity: sha512-1ibVeYUacxWYi9i0cf5efil6adJ9WRyZBLivgjs+AUpewx1F3xPi7gLgaASI2SmIQxPoCEjAsLAzKPgMJVgOUQ==}
    engines: {node: 4.x || >=6.0.0}

  '@supabase/postgrest-js@1.19.4':
    resolution: {integrity: sha512-O4soKqKtZIW3olqmbXXbKugUtByD2jPa8kL2m2c1oozAO11uCcGrRhkZL0kVxjBLrXHE0mdSkFsMj7jDSfyNpw==}

  '@supabase/realtime-js@2.11.15':
    resolution: {integrity: sha512-HQKRnwAqdVqJW/P9TjKVK+/ETpW4yQ8tyDPPtRMKOH4Uh3vQD74vmj353CYs8+YwVBKubeUOOEpI9CT8mT4obw==}

  '@supabase/ssr@0.6.1':
    resolution: {integrity: sha512-QtQgEMvaDzr77Mk3vZ3jWg2/y+D8tExYF7vcJT+wQ8ysuvOeGGjYbZlvj5bHYsj/SpC0bihcisnwPrM4Gp5G4g==}
    peerDependencies:
      '@supabase/supabase-js': ^2.43.4

  '@supabase/storage-js@2.7.1':
    resolution: {integrity: sha512-asYHcyDR1fKqrMpytAS1zjyEfvxuOIp1CIXX7ji4lHHcJKqyk+sLl/Vxgm4sN6u8zvuUtae9e4kDxQP2qrwWBA==}

  '@supabase/supabase-js@2.50.3':
    resolution: {integrity: sha512-Ld42AbfSXKnbCE2ObRvrGC5wj9OrfTOzswQZg0OcGQGx+QqcWYN/IqsLqrt4gCFrD57URbNRfGESSWzchzKAuQ==}

  '@sveltejs/acorn-typescript@1.0.5':
    resolution: {integrity: sha512-IwQk4yfwLdibDlrXVE04jTZYlLnwsTT2PIOQQGNLWfjavGifnk1JD1LcZjZaBTRcxZu2FfPfNLOE04DSu9lqtQ==}
    peerDependencies:
      acorn: ^8.9.0

  '@sveltejs/adapter-vercel@5.7.2':
    resolution: {integrity: sha512-YuFi8qgetcmvlXLqMPDKrloH/bLq31DYHSQHS2oLujES+PomJIeE7/JxYhxFStauL9QDqQ8PwOFwcZrdU/vVtg==}
    peerDependencies:
      '@sveltejs/kit': ^2.4.0

  '@sveltejs/kit@2.22.3':
    resolution: {integrity: sha512-/mwg/YcbLBT5rrdq8wvdFras5NrY4VzV1uZdQnfaowa2mIfUJAauBQkjnmXD6F1BGAYzmn+hp0lIbmn6vWhD5w==}
    engines: {node: '>=18.13'}
    hasBin: true
    peerDependencies:
      '@sveltejs/vite-plugin-svelte': ^3.0.0 || ^4.0.0-next.1 || ^5.0.0 || ^6.0.0-next.0
      svelte: ^4.0.0 || ^5.0.0-next.0
      vite: ^5.0.3 || ^6.0.0 || ^7.0.0-beta.0

  '@sveltejs/vite-plugin-svelte-inspector@4.0.1':
    resolution: {integrity: sha512-J/Nmb2Q2y7mck2hyCX4ckVHcR5tu2J+MtBEQqpDrrgELZ2uvraQcK/ioCV61AqkdXFgriksOKIceDcQmqnGhVw==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22}
    peerDependencies:
      '@sveltejs/vite-plugin-svelte': ^5.0.0
      svelte: ^5.0.0
      vite: ^6.0.0

  '@sveltejs/vite-plugin-svelte@5.1.0':
    resolution: {integrity: sha512-wojIS/7GYnJDYIg1higWj2ROA6sSRWvcR1PO/bqEyFr/5UZah26c8Cz4u0NaqjPeVltzsVpt2Tm8d2io0V+4Tw==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22}
    peerDependencies:
      svelte: ^5.0.0
      vite: ^6.0.0

  '@swc/helpers@0.5.17':
    resolution: {integrity: sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==}

  '@tailwindcss/container-queries@0.1.1':
    resolution: {integrity: sha512-p18dswChx6WnTSaJCSGx6lTmrGzNNvm2FtXmiO6AuA1V4U5REyoqwmT6kgAsIMdjo07QdAfYXHJ4hnMtfHzWgA==}
    peerDependencies:
      tailwindcss: '>=3.2.0'

  '@tailwindcss/forms@0.5.10':
    resolution: {integrity: sha512-utI1ONF6uf/pPNO68kmN1b8rEwNXv3czukalo8VtJH8ksIkZXr3Q3VYudZLkCsDd4Wku120uF02hYK25XGPorw==}
    peerDependencies:
      tailwindcss: '>=3.0.0 || >= 3.0.0-alpha.1 || >= 4.0.0-alpha.20 || >= 4.0.0-beta.1'

  '@tailwindcss/node@4.1.11':
    resolution: {integrity: sha512-yzhzuGRmv5QyU9qLNg4GTlYI6STedBWRE7NjxP45CsFYYq9taI0zJXZBMqIC/c8fViNLhmrbpSFS57EoxUmD6Q==}

  '@tailwindcss/oxide-android-arm64@4.1.11':
    resolution: {integrity: sha512-3IfFuATVRUMZZprEIx9OGDjG3Ou3jG4xQzNTvjDoKmU9JdmoCohQJ83MYd0GPnQIu89YoJqvMM0G3uqLRFtetg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [android]

  '@tailwindcss/oxide-darwin-arm64@4.1.11':
    resolution: {integrity: sha512-ESgStEOEsyg8J5YcMb1xl8WFOXfeBmrhAwGsFxxB2CxY9evy63+AtpbDLAyRkJnxLy2WsD1qF13E97uQyP1lfQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@tailwindcss/oxide-darwin-x64@4.1.11':
    resolution: {integrity: sha512-EgnK8kRchgmgzG6jE10UQNaH9Mwi2n+yw1jWmof9Vyg2lpKNX2ioe7CJdf9M5f8V9uaQxInenZkOxnTVL3fhAw==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@tailwindcss/oxide-freebsd-x64@4.1.11':
    resolution: {integrity: sha512-xdqKtbpHs7pQhIKmqVpxStnY1skuNh4CtbcyOHeX1YBE0hArj2romsFGb6yUmzkq/6M24nkxDqU8GYrKrz+UcA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [freebsd]

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11':
    resolution: {integrity: sha512-ryHQK2eyDYYMwB5wZL46uoxz2zzDZsFBwfjssgB7pzytAeCCa6glsiJGjhTEddq/4OsIjsLNMAiMlHNYnkEEeg==}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.11':
    resolution: {integrity: sha512-mYwqheq4BXF83j/w75ewkPJmPZIqqP1nhoghS9D57CLjsh3Nfq0m4ftTotRYtGnZd3eCztgbSPJ9QhfC91gDZQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@tailwindcss/oxide-linux-arm64-musl@4.1.11':
    resolution: {integrity: sha512-m/NVRFNGlEHJrNVk3O6I9ggVuNjXHIPoD6bqay/pubtYC9QIdAMpS+cswZQPBLvVvEF6GtSNONbDkZrjWZXYNQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@tailwindcss/oxide-linux-x64-gnu@4.1.11':
    resolution: {integrity: sha512-YW6sblI7xukSD2TdbbaeQVDysIm/UPJtObHJHKxDEcW2exAtY47j52f8jZXkqE1krdnkhCMGqP3dbniu1Te2Fg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@tailwindcss/oxide-linux-x64-musl@4.1.11':
    resolution: {integrity: sha512-e3C/RRhGunWYNC3aSF7exsQkdXzQ/M+aYuZHKnw4U7KQwTJotnWsGOIVih0s2qQzmEzOFIJ3+xt7iq67K/p56Q==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@tailwindcss/oxide-wasm32-wasi@4.1.11':
    resolution: {integrity: sha512-Xo1+/GU0JEN/C/dvcammKHzeM6NqKovG+6921MR6oadee5XPBaKOumrJCXvopJ/Qb5TH7LX/UAywbqrP4lax0g==}
    engines: {node: '>=14.0.0'}
    cpu: [wasm32]
    bundledDependencies:
      - '@napi-rs/wasm-runtime'
      - '@emnapi/core'
      - '@emnapi/runtime'
      - '@tybys/wasm-util'
      - '@emnapi/wasi-threads'
      - tslib

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.11':
    resolution: {integrity: sha512-UgKYx5PwEKrac3GPNPf6HVMNhUIGuUh4wlDFR2jYYdkX6pL/rn73zTq/4pzUm8fOjAn5L8zDeHp9iXmUGOXZ+w==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    resolution: {integrity: sha512-YfHoggn1j0LK7wR82TOucWc5LDCguHnoS879idHekmmiR7g9HUtMw9MI0NHatS28u/Xlkfi9w5RJWgz2Dl+5Qg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@tailwindcss/oxide@4.1.11':
    resolution: {integrity: sha512-Q69XzrtAhuyfHo+5/HMgr1lAiPP/G40OMFAnws7xcFEYqcypZmdW8eGXaOUIeOl1dzPJBPENXgbjsOyhg2nkrg==}
    engines: {node: '>= 10'}

  '@tailwindcss/typography@0.5.16':
    resolution: {integrity: sha512-0wDLwCVF5V3x3b1SGXPCDcdsbDHMBe+lkFzBRaHeLvNi+nrrnZ1lA18u+OTWO8iSWU2GxUOCvlXtDuqftc1oiA==}
    peerDependencies:
      tailwindcss: '>=3.0.0 || insiders || >=4.0.0-alpha.20 || >=4.0.0-beta.1'

  '@tailwindcss/vite@4.1.11':
    resolution: {integrity: sha512-RHYhrR3hku0MJFRV+fN2gNbDNEh3dwKvY8XJvTxCSXeMOsCRSr+uKvDWQcbizrHgjML6ZmTE5OwMrl5wKcujCw==}
    peerDependencies:
      vite: ^5.2.0 || ^6 || ^7

  '@tanstack/table-core@8.21.3':
    resolution: {integrity: sha512-ldZXEhOBb8Is7xLs01fR3YEc3DERiz5silj8tnGkFZytt1abEvl/GhUmCE0PMLaMPTa3Jk4HbKmRlHmu+gCftg==}
    engines: {node: '>=12'}

  '@testing-library/dom@10.4.0':
    resolution: {integrity: sha512-pemlzrSESWbdAloYml3bAJMEfNh1Z7EduzqPKprCH5S341frlpYnUEW0H72dLxa6IsYr+mPno20GiSm+h9dEdQ==}
    engines: {node: '>=18'}

  '@testing-library/jest-dom@6.6.3':
    resolution: {integrity: sha512-IteBhl4XqYNkM54f4ejhLRJiZNqcSCoXUOG2CPK7qbD322KjQozM4kHQOfkG2oln9b9HTYqs+Sae8vBATubxxA==}
    engines: {node: '>=14', npm: '>=6', yarn: '>=1'}

  '@testing-library/user-event@14.6.1':
    resolution: {integrity: sha512-vq7fv0rnt+QTXgPxr5Hjc210p6YKq2kmdziLgnsZGgLJ9e6VAShx1pACLuRjd/AS/sr7phAR58OIIpf0LlmQNw==}
    engines: {node: '>=12', npm: '>=6'}
    peerDependencies:
      '@testing-library/dom': '>=7.21.4'

  '@types/aria-query@5.0.4':
    resolution: {integrity: sha512-rfT93uj5s0PRL7EzccGMs3brplhcrghnDoV26NqKhCAS1hVo+WdNsPvE/yb6ilfr5hi2MEk6d5EWJTKdxg8jVw==}

  '@types/chai@5.2.2':
    resolution: {integrity: sha512-8kB30R7Hwqf40JPiKhVzodJs2Qc1ZJ5zuT3uzw5Hq/dhNCl3G3l83jfpdI1e20BP348+fV7VIL/+FxaXkqBmWg==}

  '@types/connect@3.4.38':
    resolution: {integrity: sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==}

  '@types/cookie@0.6.0':
    resolution: {integrity: sha512-4Kh9a6B2bQciAhf7FSuMRRkUWecJgJu9nPnx3yzpsfXX/c50REIqpHY4C82bXP90qrLtXtkDxTZosYO3UpOwlA==}

  '@types/d3-color@3.1.3':
    resolution: {integrity: sha512-iO90scth9WAbmgv7ogoq57O9YpKmFBbmoEoCHDB2xMBY0+/KVrqAaCDyCE16dUspeOvIxFFRI+0sEtqDqy2b4A==}

  '@types/d3-hierarchy@3.1.7':
    resolution: {integrity: sha512-tJFtNoYBtRtkNysX1Xq4sxtjK8YgoWUNpIiUee0/jHGRwqvzYxkq0hGVbbOGSz+JgFxxRu4K8nb3YpG3CMARtg==}

  '@types/d3-scale-chromatic@3.1.0':
    resolution: {integrity: sha512-iWMJgwkK7yTRmWqRB5plb1kadXyQ5Sj8V/zYlFGMUBbIPKQScw+Dku9cAAMgJG+z5GYDoMjWGLVOvjghDEFnKQ==}

  '@types/d3-scale@4.0.9':
    resolution: {integrity: sha512-dLmtwB8zkAeO/juAMfnV+sItKjlsw2lKdZVVy6LRr0cBmegxSABiLEpGVmSJJ8O08i4+sGR6qQtb6WtuwJdvVw==}

  '@types/d3-time@3.0.4':
    resolution: {integrity: sha512-yuzZug1nkAAaBlBBikKZTgzCeA+k1uy4ZFwWANOfKw5z5LRhV0gNA7gNkKm7HoK+HRN0wX3EkxGk0fpbWhmB7g==}

  '@types/deep-eql@4.0.2':
    resolution: {integrity: sha512-c9h9dVVMigMPc4bwTvC5dxqtqJZwQPePsWjPlpSOnojbor6pGqdk541lfA7AqFQr5pB1BRdq0juY9db81BwyFw==}

  '@types/estree@1.0.8':
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/mdx@2.0.13':
    resolution: {integrity: sha512-+OWZQfAYyio6YkJb3HLxDrvnx6SWWDbC0zVPfBRzUk0/nqoDyf6dNxQi3eArPe8rJ473nobTMQ/8Zk+LxJ+Yuw==}

  '@types/mysql@2.15.26':
    resolution: {integrity: sha512-DSLCOXhkvfS5WNNPbfn2KdICAmk8lLc+/PNvnPnF7gOdMZCxopXduqv0OQ13y/yA/zXTSikZZqVgybUxOEg6YQ==}

  '@types/node@24.0.12':
    resolution: {integrity: sha512-LtOrbvDf5ndC9Xi+4QZjVL0woFymF/xSTKZKPgrrl7H7XoeDvnD+E2IclKVDyaK9UM756W/3BXqSU+JEHopA9g==}

  '@types/pegjs@0.10.6':
    resolution: {integrity: sha512-eLYXDbZWXh2uxf+w8sXS8d6KSoXTswfps6fvCUuVAGN8eRpfe7h9eSRydxiSJvo9Bf+GzifsDOr9TMQlmJdmkw==}

  '@types/pg-pool@2.0.6':
    resolution: {integrity: sha512-TaAUE5rq2VQYxab5Ts7WZhKNmuN78Q6PiFonTDdpbx8a1H0M1vhy3rhiMjl+e2iHmogyMw7jZF4FrE6eJUy5HQ==}

  '@types/pg@8.6.1':
    resolution: {integrity: sha512-1Kc4oAGzAl7uqUStZCDvaLFqZrW9qWSjXOmBfdgyBP5La7Us6Mg4GBvRlSoaZMhQF/zSj1C8CtKMBkoiT8eL8w==}

  '@types/phoenix@1.6.6':
    resolution: {integrity: sha512-PIzZZlEppgrpoT2QgbnDU+MMzuR6BbCjllj0bM70lWoejMeNJAxCchxnv7J3XFkI8MpygtRpzXrIlmWUBclP5A==}

  '@types/react@19.1.8':
    resolution: {integrity: sha512-AwAfQ2Wa5bCx9WP8nZL2uMZWod7J7/JSplxbTmBQ5ms6QpqNYm672H0Vu9ZVKVngQ+ii4R/byguVEUZQyeg44g==}

  '@types/shimmer@1.2.0':
    resolution: {integrity: sha512-UE7oxhQLLd9gub6JKIAhDq06T0F6FnztwMNRvYgjeQSBeMc1ZG/tA47EwfduvkuQS8apbkM/lpLpWsaCeYsXVg==}

  '@types/tedious@4.0.14':
    resolution: {integrity: sha512-KHPsfX/FoVbUGbyYvk1q9MMQHLPeRZhRJZdO45Q4YjvFkv4hMNghCWTvy7rdKessBsmtz4euWCWAB6/tVpI1Iw==}

  '@types/validator@13.15.2':
    resolution: {integrity: sha512-y7pa/oEJJ4iGYBxOpfAKn5b9+xuihvzDVnC/OSvlVnGxVg0pOqmjiMafiJ1KVNQEaPZf9HsEp5icEwGg8uIe5Q==}

  '@types/ws@8.18.1':
    resolution: {integrity: sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==}

  '@typeschema/class-validator@0.3.0':
    resolution: {integrity: sha512-OJSFeZDIQ8EK1HTljKLT5CItM2wsbgczLN8tMEfz3I1Lmhc5TBfkZ0eikFzUC16tI3d1Nag7um6TfCgp2I2Bww==}
    peerDependencies:
      class-validator: ^0.14.1
    peerDependenciesMeta:
      class-validator:
        optional: true

  '@typeschema/core@0.14.0':
    resolution: {integrity: sha512-Ia6PtZHcL3KqsAWXjMi5xIyZ7XMH4aSnOQes8mfMLx+wGFGtGRNlwe6Y7cYvX+WfNK67OL0/HSe9t8QDygV0/w==}
    peerDependencies:
      '@types/json-schema': ^7.0.15
    peerDependenciesMeta:
      '@types/json-schema':
        optional: true

  '@typescript-eslint/eslint-plugin@8.36.0':
    resolution: {integrity: sha512-lZNihHUVB6ZZiPBNgOQGSxUASI7UJWhT8nHyUGCnaQ28XFCw98IfrMCG3rUl1uwUWoAvodJQby2KTs79UTcrAg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.36.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/parser@8.36.0':
    resolution: {integrity: sha512-FuYgkHwZLuPbZjQHzJXrtXreJdFMKl16BFYyRrLxDhWr6Qr7Kbcu2s1Yhu8tsiMXw1S0W1pjfFfYEt+R604s+Q==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/project-service@8.36.0':
    resolution: {integrity: sha512-JAhQFIABkWccQYeLMrHadu/fhpzmSQ1F1KXkpzqiVxA/iYI6UnRt2trqXHt1sYEcw1mxLnB9rKMsOxXPxowN/g==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/scope-manager@8.36.0':
    resolution: {integrity: sha512-wCnapIKnDkN62fYtTGv2+RY8FlnBYA3tNm0fm91kc2BjPhV2vIjwwozJ7LToaLAyb1ca8BxrS7vT+Pvvf7RvqA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/tsconfig-utils@8.36.0':
    resolution: {integrity: sha512-Nhh3TIEgN18mNbdXpd5Q8mSCBnrZQeY9V7Ca3dqYvNDStNIGRmJA6dmrIPMJ0kow3C7gcQbpsG2rPzy1Ks/AnA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/type-utils@8.36.0':
    resolution: {integrity: sha512-5aaGYG8cVDd6cxfk/ynpYzxBRZJk7w/ymto6uiyUFtdCozQIsQWh7M28/6r57Fwkbweng8qAzoMCPwSJfWlmsg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/types@8.36.0':
    resolution: {integrity: sha512-xGms6l5cTJKQPZOKM75Dl9yBfNdGeLRsIyufewnxT4vZTrjC0ImQT4fj8QmtJK84F58uSh5HVBSANwcfiXxABQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/typescript-estree@8.36.0':
    resolution: {integrity: sha512-JaS8bDVrfVJX4av0jLpe4ye0BpAaUW7+tnS4Y4ETa3q7NoZgzYbN9zDQTJ8kPb5fQ4n0hliAt9tA4Pfs2zA2Hg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/utils@8.36.0':
    resolution: {integrity: sha512-VOqmHu42aEMT+P2qYjylw6zP/3E/HvptRwdn/PZxyV27KhZg2IOszXod4NcXisWzPAGSS4trE/g4moNj6XmH2g==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/visitor-keys@8.36.0':
    resolution: {integrity: sha512-vZrhV2lRPWDuGoxcmrzRZyxAggPL+qp3WzUrlZD+slFueDiYHxeBa34dUXPuC0RmGKzl4lS5kFJYvKCq9cnNDA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@vercel/nft@0.29.4':
    resolution: {integrity: sha512-6lLqMNX3TuycBPABycx7A9F1bHQR7kiQln6abjFbPrf5C/05qHM9M5E4PeTE59c7z8g6vHnx1Ioihb2AQl7BTA==}
    engines: {node: '>=18'}
    hasBin: true

  '@vinejs/compiler@3.0.0':
    resolution: {integrity: sha512-v9Lsv59nR56+bmy2p0+czjZxsLHwaibJ+SV5iK9JJfehlJMa501jUJQqqz4X/OqKXrxtE3uTQmSqjUqzF3B2mw==}
    engines: {node: '>=18.0.0'}

  '@vinejs/vine@3.0.1':
    resolution: {integrity: sha512-ZtvYkYpZOYdvbws3uaOAvTFuvFXoQGAtmzeiXu+XSMGxi5GVsODpoI9Xu9TplEMuD/5fmAtBbKb9cQHkWkLXDQ==}
    engines: {node: '>=18.16.0'}

  '@vitest/browser@3.2.4':
    resolution: {integrity: sha512-tJxiPrWmzH8a+w9nLKlQMzAKX/7VjFs50MWgcAj7p9XQ7AQ9/35fByFYptgPELyLw+0aixTnC4pUWV+APcZ/kw==}
    peerDependencies:
      playwright: '*'
      safaridriver: '*'
      vitest: 3.2.4
      webdriverio: ^7.0.0 || ^8.0.0 || ^9.0.0
    peerDependenciesMeta:
      playwright:
        optional: true
      safaridriver:
        optional: true
      webdriverio:
        optional: true

  '@vitest/coverage-v8@3.2.4':
    resolution: {integrity: sha512-EyF9SXU6kS5Ku/U82E259WSnvg6c8KTjppUncuNdm5QHpe17mwREHnjDzozC8x9MZ0xfBUFSaLkRv4TMA75ALQ==}
    peerDependencies:
      '@vitest/browser': 3.2.4
      vitest: 3.2.4
    peerDependenciesMeta:
      '@vitest/browser':
        optional: true

  '@vitest/expect@3.0.9':
    resolution: {integrity: sha512-5eCqRItYgIML7NNVgJj6TVCmdzE7ZVgJhruW0ziSQV4V7PvLkDL1bBkBdcTs/VuIz0IxPb5da1IDSqc1TR9eig==}

  '@vitest/expect@3.2.4':
    resolution: {integrity: sha512-Io0yyORnB6sikFlt8QW5K7slY4OjqNX9jmJQ02QDda8lyM6B5oNgVWoSoKPac8/kgnCUzuHQKrSLtu/uOqqrig==}

  '@vitest/mocker@3.2.4':
    resolution: {integrity: sha512-46ryTE9RZO/rfDd7pEqFl7etuyzekzEhUbTW3BvmeO/BcCMEgq59BKhek3dXDWgAj4oMK6OZi+vRr1wPW6qjEQ==}
    peerDependencies:
      msw: ^2.4.9
      vite: ^5.0.0 || ^6.0.0 || ^7.0.0-0
    peerDependenciesMeta:
      msw:
        optional: true
      vite:
        optional: true

  '@vitest/pretty-format@3.0.9':
    resolution: {integrity: sha512-OW9F8t2J3AwFEwENg3yMyKWweF7oRJlMyHOMIhO5F3n0+cgQAJZBjNgrF8dLwFTEXl5jUqBLXd9QyyKv8zEcmA==}

  '@vitest/pretty-format@3.2.4':
    resolution: {integrity: sha512-IVNZik8IVRJRTr9fxlitMKeJeXFFFN0JaB9PHPGQ8NKQbGpfjlTx9zO4RefN8gp7eqjNy8nyK3NZmBzOPeIxtA==}

  '@vitest/runner@3.2.4':
    resolution: {integrity: sha512-oukfKT9Mk41LreEW09vt45f8wx7DordoWUZMYdY/cyAk7w5TWkTRCNZYF7sX7n2wB7jyGAl74OxgwhPgKaqDMQ==}

  '@vitest/snapshot@3.2.4':
    resolution: {integrity: sha512-dEYtS7qQP2CjU27QBC5oUOxLE/v5eLkGqPE0ZKEIDGMs4vKWe7IjgLOeauHsR0D5YuuycGRO5oSRXnwnmA78fQ==}

  '@vitest/spy@3.0.9':
    resolution: {integrity: sha512-/CcK2UDl0aQ2wtkp3YVWldrpLRNCfVcIOFGlVGKO4R5eajsH393Z1yiXLVQ7vWsj26JOEjeZI0x5sm5P4OGUNQ==}

  '@vitest/spy@3.2.4':
    resolution: {integrity: sha512-vAfasCOe6AIK70iP5UD11Ac4siNUNJ9i/9PZ3NKx07sG6sUxeag1LWdNrMWeKKYBLlzuK+Gn65Yd5nyL6ds+nw==}

  '@vitest/utils@3.0.9':
    resolution: {integrity: sha512-ilHM5fHhZ89MCp5aAaM9uhfl1c2JdxVxl3McqsdVyVNN6JffnEen8UMCdRTzOhGXNQGo5GNL9QugHrz727Wnng==}

  '@vitest/utils@3.2.4':
    resolution: {integrity: sha512-fB2V0JFrQSMsCo9HiSq3Ezpdv4iYaXRG1Sx8edX3MwxfyNn83mKiGzOcH+Fkxt4MHxr3y42fQi1oeAInqgX2QA==}

  abbrev@3.0.1:
    resolution: {integrity: sha512-AO2ac6pjRB3SJmGJo+v5/aK6Omggp6fsLrs6wN9bd35ulu4cCwaAU9+7ZhXjeqHVkaHThLuzH0nZr0YpCDhygg==}
    engines: {node: ^18.17.0 || >=20.5.0}

  acorn-import-attributes@1.9.5:
    resolution: {integrity: sha512-n02Vykv5uA3eHGM/Z2dQrcD56kL8TyDb2p1+0P83PClMnC/nc+anbQRhIOWnSq4Ke/KvDPrY3C9hDtC/A3eHnQ==}
    peerDependencies:
      acorn: ^8

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.15.0:
    resolution: {integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  agent-base@6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==}
    engines: {node: '>= 6.0.0'}

  agent-base@7.1.4:
    resolution: {integrity: sha512-MnA+YT8fwfJPgBx3m60MNqakm30XOkyIoH1y6huTQvC0PwZG7ki8NacLBcrPbNoo8vEZy7Jpuk7+jMO+CUovTQ==}
    engines: {node: '>= 14'}

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==}
    engines: {node: '>=12'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==}
    engines: {node: '>=10'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  aria-query@5.3.0:
    resolution: {integrity: sha512-b0P0sZPKtyu8HkeRAfCq0IfURZK+SuwMjY1UXGBU27wpAiTwQAIlq56IbIO+ytk/JjS1fMR14ee5WBBfKi5J6A==}

  aria-query@5.3.2:
    resolution: {integrity: sha512-COROpnaoap1E2F000S62r6A60uHZnmlvomhfyT2DlTcrY1OrBKn2UhH7qn5wTC9zMvD0AY7csdPSNwKP+7WiQw==}
    engines: {node: '>= 0.4'}

  arktype@2.1.20:
    resolution: {integrity: sha512-IZCEEXaJ8g+Ijd59WtSYwtjnqXiwM8sWQ5EjGamcto7+HVN9eK0C4p0zDlCuAwWhpqr6fIBkxPuYDl4/Mcj/+Q==}

  assertion-error@2.0.1:
    resolution: {integrity: sha512-Izi8RQcffqCeNVgFigKli1ssklIbpHnCYc6AknXGYoB6grJqyeby7jv12JUQgmTAnIDnbck1uxksT4dzN3PWBA==}
    engines: {node: '>=12'}

  ast-types@0.16.1:
    resolution: {integrity: sha512-6t10qk83GOG8p0vKmaCr8eiilZwO171AvbROMtvvNiwrTly62t+7XkA8RdIIVbpMhCASAsxgAzdRSwh6nw/5Dg==}
    engines: {node: '>=4'}

  ast-v8-to-istanbul@0.3.3:
    resolution: {integrity: sha512-MuXMrSLVVoA6sYN/6Hke18vMzrT4TZNbZIj/hvh0fnYFpO+/kFXcLIaiPwXXWaQUPg4yJD8fj+lfJ7/1EBconw==}

  async-sema@3.1.1:
    resolution: {integrity: sha512-tLRNUXati5MFePdAk8dw7Qt7DpxPB60ofAgn8WRhW6a2rcimZnYBP9oxHiv0OHy+Wz7kPMG+t4LGdt31+4EmGg==}

  axobject-query@4.1.0:
    resolution: {integrity: sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ==}
    engines: {node: '>= 0.4'}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  better-opn@3.0.2:
    resolution: {integrity: sha512-aVNobHnJqLiUelTaHat9DZ1qM2w0C0Eym4LPI/3JxOnSokGVdsl1T1kN7TFvsEAD8G47A6VKQ0TVHqbBnYMJlQ==}
    engines: {node: '>=12.0.0'}

  big-integer@1.6.52:
    resolution: {integrity: sha512-QxD8cf2eVqJOOz63z6JIN9BzvVs/dlySa5HGSBH5xtR8dPteIRQnBxxKqkNTiT6jbDTF6jAfrd4oMcND9RGbQg==}
    engines: {node: '>=0.6'}

  bin-links@5.0.0:
    resolution: {integrity: sha512-sdleLVfCjBtgO5cNjA2HVRvWBJAHs4zwenaCPMNJAJU0yNxpzj80IpjOIimkpkr+mhlA+how5poQtt53PygbHA==}
    engines: {node: ^18.17.0 || >=20.5.0}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  bindings@1.5.0:
    resolution: {integrity: sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==}

  bits-ui@2.8.10:
    resolution: {integrity: sha512-MOobkqapDZNrpcNmeL2g664xFmH4tZBOKBTxFmsQYMZQuybSZHQnPXy+AjM5XZEXRmCFx5+XRmo6+fC3vHh1hQ==}
    engines: {node: '>=20'}
    peerDependencies:
      '@internationalized/date': ^3.8.1
      svelte: ^5.33.0

  brace-expansion@1.1.12:
    resolution: {integrity: sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==}

  brace-expansion@2.0.2:
    resolution: {integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  browserslist@4.25.1:
    resolution: {integrity: sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  cac@6.7.14:
    resolution: {integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==}
    engines: {node: '>=8'}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camelcase@8.0.0:
    resolution: {integrity: sha512-8WB3Jcas3swSvjIeA2yvCJ+Miyz5l1ZmB6HFb9R1317dt9LCQoswg/BGrmAmkWVEszSrrg4RwmO46qIm2OEnSA==}
    engines: {node: '>=16'}

  caniuse-lite@1.0.30001727:
    resolution: {integrity: sha512-pB68nIHmbN6L/4C6MH1DokyR3bYqFwjaSs/sWDHGj4CTcFtQUQMuJftVwWkXq7mNWOybD3KhUv3oWHoGxgP14Q==}

  chai@5.2.1:
    resolution: {integrity: sha512-5nFxhUrX0PqtyogoYOA8IPswy5sZFTOsBFl/9bNsmDLgsxYTzSZQJDPppDnZPTQbzSEm0hqGjWPzRemQCYbD6A==}
    engines: {node: '>=18'}

  chalk@3.0.0:
    resolution: {integrity: sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==}
    engines: {node: '>=8'}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  check-error@2.1.1:
    resolution: {integrity: sha512-OAlb+T7V4Op9OwdkjmguYRqncdlx5JiofwOAUkmTF+jNdHwzTaTs4sRAGpzLF3oOz5xAyDGrPgeIDFQmDOTiJw==}
    engines: {node: '>= 16'}

  chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}

  chokidar@4.0.3:
    resolution: {integrity: sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==}
    engines: {node: '>= 14.16.0'}

  chownr@3.0.0:
    resolution: {integrity: sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g==}
    engines: {node: '>=18'}

  chromatic@12.2.0:
    resolution: {integrity: sha512-GswmBW9ZptAoTns1BMyjbm55Z7EsIJnUvYKdQqXIBZIKbGErmpA+p4c0BYA+nzw5B0M+rb3Iqp1IaH8TFwIQew==}
    hasBin: true
    peerDependencies:
      '@chromatic-com/cypress': ^0.*.* || ^1.0.0
      '@chromatic-com/playwright': ^0.*.* || ^1.0.0
    peerDependenciesMeta:
      '@chromatic-com/cypress':
        optional: true
      '@chromatic-com/playwright':
        optional: true

  cjs-module-lexer@1.4.3:
    resolution: {integrity: sha512-9z8TZaGM1pfswYeXrUpzPrkx8UnWYdhJclsiYMm6x/w5+nN+8Tf/LnAgfLGQCm59qAOxU8WwHEq2vNwF6i4j+Q==}

  class-validator@0.14.2:
    resolution: {integrity: sha512-3kMVRF2io8N8pY1IFIXlho9r8IPUUIfHe2hYVtiebvAzU2XeQFXTv+XI4WX+TnXmtwXMDcjngcpkiPM0O9PvLw==}

  clsx@2.1.1:
    resolution: {integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==}
    engines: {node: '>=6'}

  cmd-shim@7.0.0:
    resolution: {integrity: sha512-rtpaCbr164TPPh+zFdkWpCyZuKkjpAzODfaZCf/SVJZzJN+4bHQb/LP3Jzq5/+84um3XXY8r548XiWKSborwVw==}
    engines: {node: ^18.17.0 || >=20.5.0}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  commander@7.2.0:
    resolution: {integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==}
    engines: {node: '>= 10'}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  consola@3.4.2:
    resolution: {integrity: sha512-5IKcdX0nnYavi6G7TtOhwkYzyjfJlatbjMjuLSfE2kYT5pMDOilZ4OvMhi637CcDICTmz3wARPoyhqyX1Y+XvA==}
    engines: {node: ^14.18.0 || >=16.10.0}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  cookie@0.6.0:
    resolution: {integrity: sha512-U71cyTamuh1CRNCfpGY6to28lxvNwPG4Guz/EVjgf3Jmzv0vlDp1atT9eS5dDjMYHucpHbWns6Lwf3BKz6svdw==}
    engines: {node: '>= 0.6'}

  cookie@1.0.2:
    resolution: {integrity: sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==}
    engines: {node: '>=18'}

  cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}

  css.escape@1.5.1:
    resolution: {integrity: sha512-YUifsXXuknHlUsmlgyY0PKzgPOr7/FjCePfHNt0jxm83wHZi44VDMQ7/fGNkjY3/jV1MC+1CmZbaHzugyeRtpg==}

  cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  culori@4.0.2:
    resolution: {integrity: sha512-1+BhOB8ahCn4O0cep0Sh2l9KCOfOdY+BXJnKMHFFzDEouSr/el18QwXEMRlOj9UY5nCeA8UN3a/82rUWRBeyBw==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  d3-array@2.12.1:
    resolution: {integrity: sha512-B0ErZK/66mHtEsR1TkPEEkwdy+WDesimkM5gpZr5Dsg54BiTA5RXtYW5qTLIAcekaS9xfZrzBLF/OAkB3Qn1YQ==}

  d3-array@3.2.4:
    resolution: {integrity: sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==}
    engines: {node: '>=12'}

  d3-color@3.1.0:
    resolution: {integrity: sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==}
    engines: {node: '>=12'}

  d3-delaunay@6.0.4:
    resolution: {integrity: sha512-mdjtIZ1XLAM8bm/hx3WwjfHt6Sggek7qH043O8KEjDXN40xi3vx/6pYSVTwLjEgiXQTbvaouWKynLBiUZ6SK6A==}
    engines: {node: '>=12'}

  d3-dispatch@3.0.1:
    resolution: {integrity: sha512-rzUyPU/S7rwUflMyLc1ETDeBj0NRuHKKAcvukozwhshr6g6c5d8zh4c2gQjY2bZ0dXeGLWc1PF174P2tVvKhfg==}
    engines: {node: '>=12'}

  d3-dsv@3.0.1:
    resolution: {integrity: sha512-UG6OvdI5afDIFP9w4G0mNq50dSOsXHJaRE8arAS5o9ApWnIElp8GZw1Dun8vP8OyHOZ/QJUKUJwxiiCCnUwm+Q==}
    engines: {node: '>=12'}
    hasBin: true

  d3-force@3.0.0:
    resolution: {integrity: sha512-zxV/SsA+U4yte8051P4ECydjD/S+qeYtnaIyAs9tgHCqfguma/aAQDjo85A9Z6EKhBirHRJHXIgJUlffT4wdLg==}
    engines: {node: '>=12'}

  d3-format@3.1.0:
    resolution: {integrity: sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==}
    engines: {node: '>=12'}

  d3-geo-voronoi@2.1.0:
    resolution: {integrity: sha512-kqE4yYuOjPbKdBXG0xztCacPwkVSK2REF1opSNrnqqtXJmNcM++UbwQ8SxvwP6IQTj9RvIjjK4qeiVsEfj0Z2Q==}
    engines: {node: '>=12'}

  d3-geo@3.1.1:
    resolution: {integrity: sha512-637ln3gXKXOwhalDzinUgY83KzNWZRKbYubaG+fGVuc/dxO64RRljtCTnf5ecMyE1RIdtqpkVcq0IbtU2S8j2Q==}
    engines: {node: '>=12'}

  d3-hierarchy@3.1.2:
    resolution: {integrity: sha512-FX/9frcub54beBdugHjDCdikxThEqjnR93Qt7PvQTOHxyiNCAlvMrHhclk3cD5VeAaq9fxmfRp+CnWw9rEMBuA==}
    engines: {node: '>=12'}

  d3-interpolate-path@2.3.0:
    resolution: {integrity: sha512-tZYtGXxBmbgHsIc9Wms6LS5u4w6KbP8C09a4/ZYc4KLMYYqub57rRBUgpUr2CIarIrJEpdAWWxWQvofgaMpbKQ==}

  d3-interpolate@3.0.1:
    resolution: {integrity: sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==}
    engines: {node: '>=12'}

  d3-path@1.0.9:
    resolution: {integrity: sha512-VLaYcn81dtHVTjEHd8B+pbe9yHWpXKZUC87PzoFmsFrJqgFwDe/qxfp5MlfsfM1V5E/iVt0MmEbWQ7FVIXh/bg==}

  d3-path@3.1.0:
    resolution: {integrity: sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==}
    engines: {node: '>=12'}

  d3-quadtree@3.0.1:
    resolution: {integrity: sha512-04xDrxQTDTCFwP5H6hRhsRcb9xxv2RzkcsygFzmkSIOJy3PeRJP7sNk3VRIbKXcog561P9oU0/rVH6vDROAgUw==}
    engines: {node: '>=12'}

  d3-random@3.0.1:
    resolution: {integrity: sha512-FXMe9GfxTxqd5D6jFsQ+DJ8BJS4E/fT5mqqdjovykEB2oFbTMDVdg1MGFxfQW+FBOGoB++k8swBrgwSHT1cUXQ==}
    engines: {node: '>=12'}

  d3-sankey@0.12.3:
    resolution: {integrity: sha512-nQhsBRmM19Ax5xEIPLMY9ZmJ/cDvd1BG3UVvt5h3WRxKg5zGRbvnteTyWAbzeSvlh3tW7ZEmq4VwR5mB3tutmQ==}

  d3-scale-chromatic@3.1.0:
    resolution: {integrity: sha512-A3s5PWiZ9YCXFye1o246KoscMWqf8BsD9eRiJ3He7C9OBaxKhAd5TFCdEx/7VbKtxxTsu//1mMJFrEt572cEyQ==}
    engines: {node: '>=12'}

  d3-scale@4.0.2:
    resolution: {integrity: sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==}
    engines: {node: '>=12'}

  d3-shape@1.3.7:
    resolution: {integrity: sha512-EUkvKjqPFUAZyOlhY5gzCxCeI0Aep04LwIRpsZ/mLFelJiUfnK56jo5JMDSE7yyP2kLSb6LtF+S5chMk7uqPqw==}

  d3-shape@3.2.0:
    resolution: {integrity: sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==}
    engines: {node: '>=12'}

  d3-tile@1.0.0:
    resolution: {integrity: sha512-79fnTKpPMPDS5xQ0xuS9ir0165NEwwkFpe/DSOmc2Gl9ldYzKKRDWogmTTE8wAJ8NA7PMapNfEcyKhI9Lxdu5Q==}

  d3-time-format@4.1.0:
    resolution: {integrity: sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==}
    engines: {node: '>=12'}

  d3-time@3.1.0:
    resolution: {integrity: sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==}
    engines: {node: '>=12'}

  d3-timer@3.0.1:
    resolution: {integrity: sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==}
    engines: {node: '>=12'}

  d3-tricontour@1.0.2:
    resolution: {integrity: sha512-HIRxHzHagPtUPNabjOlfcyismJYIsc+Xlq4mlsts4e8eAcwyq9Tgk/sYdyhlBpQ0MHwVquc/8j+e29YjXnmxeA==}
    engines: {node: '>=12'}

  data-uri-to-buffer@4.0.1:
    resolution: {integrity: sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A==}
    engines: {node: '>= 12'}

  date-fns@4.1.0:
    resolution: {integrity: sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg==}

  dayjs@1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==}

  debug@4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  dedent-js@1.0.1:
    resolution: {integrity: sha512-OUepMozQULMLUmhxS95Vudo0jb0UchLimi3+pQ2plj61Fcy8axbP9hbiD4Sz6DPqn6XG3kfmziVfQ1rSys5AJQ==}

  dedent@1.6.0:
    resolution: {integrity: sha512-F1Z+5UCFpmQUzJa11agbyPVMbpgT/qA3/SKyJ1jyBgm7dUcUEa8v9JwDkerSQXfakBwFljIxhOJqGkjUwZ9FSA==}
    peerDependencies:
      babel-plugin-macros: ^3.1.0
    peerDependenciesMeta:
      babel-plugin-macros:
        optional: true

  deep-eql@5.0.2:
    resolution: {integrity: sha512-h5k/5U50IJJFpzfL6nO9jaaumfjO/f2NjK/oYB2Djzm4p9L+3T9qWpZqZ2hAbLPuuYq9wrU08WQyBTL5GbPk5Q==}
    engines: {node: '>=6'}

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}

  define-lazy-prop@2.0.0:
    resolution: {integrity: sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==}
    engines: {node: '>=8'}

  delaunator@5.0.1:
    resolution: {integrity: sha512-8nvh+XBe96aCESrGOqMp/84b13H9cdKbG5P2ejQCh4d4sK9RL4371qou9drQjMhvnPmhWl5hnmqbEE0fXr9Xnw==}

  dequal@2.0.3:
    resolution: {integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==}
    engines: {node: '>=6'}

  detect-libc@2.0.4:
    resolution: {integrity: sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==}
    engines: {node: '>=8'}

  devalue@5.1.1:
    resolution: {integrity: sha512-maua5KUiapvEwiEAe+XnlZ3Rh0GD+qI1J/nb9vrJc3muPXvcF/8gXYTWF76+5DAqHyDUtOIImEuo0YKE9mshVw==}

  discontinuous-range@1.0.0:
    resolution: {integrity: sha512-c68LpLbO+7kP/b1Hr1qs8/BJ09F5khZGTxqxZuhzxpmwJKOgRFHJWIb9/KmqnqHhLdO55aOxFH/EGBvUQbL/RQ==}

  dlv@1.1.3:
    resolution: {integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==}

  dom-accessibility-api@0.5.16:
    resolution: {integrity: sha512-X7BJ2yElsnOJ30pZF4uIIDfBEVgF4XEBxL9Bxhy6dnrm5hkzqmsWHGTiHqRiITNhMyFLyAiWndIJP7Z1NTteDg==}

  dom-accessibility-api@0.6.3:
    resolution: {integrity: sha512-7ZgogeTnjuHbo+ct10G9Ffp0mif17idi0IyWNVA/wcwcm7NPOD/WEHVP3n7n3MhXqxoIYm8d6MuZohYWIZ4T3w==}

  dom-serializer@2.0.0:
    resolution: {integrity: sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==}

  domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}

  domhandler@5.0.3:
    resolution: {integrity: sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==}
    engines: {node: '>= 4'}

  domutils@3.2.2:
    resolution: {integrity: sha512-6kZKyUajlDuqlHKVX1w7gyslj9MPIXzIFiz/rGu35uC1wMi+kMhQwGhl4lt9unC9Vb9INnY9Z3/ZA3+FhASLaw==}

  dotenv@16.6.1:
    resolution: {integrity: sha512-uBq4egWHTcTt33a72vpSG0z3HnPuIl6NqYcTrKEg2azoEyl2hpW0zqlxysq2pK9HlDIHyHyakeYaYnSAwd8bow==}
    engines: {node: '>=12'}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  effect@3.16.12:
    resolution: {integrity: sha512-N39iBk0K71F9nb442TLbTkjl24FLUzuvx2i1I2RsEAQsdAdUTuUoW0vlfUXgkMTUOnYqKnWcFfqw4hK4Pw27hg==}

  electron-to-chromium@1.5.180:
    resolution: {integrity: sha512-ED+GEyEh3kYMwt2faNmgMB0b8O5qtATGgR4RmRsIp4T6p7B8vdMbIedYndnvZfsaXvSzegtpfqRMDNCjjiSduA==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  enhanced-resolve@5.18.2:
    resolution: {integrity: sha512-6Jw4sE1maoRJo3q8MsSIn2onJFbLTOjY9hlx4DZXmOKvLRd1Ok2kXmAGXaafL2+ijsJZ1ClYbl/pmqr9+k4iUQ==}
    engines: {node: '>=10.13.0'}

  entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  es-module-lexer@1.7.0:
    resolution: {integrity: sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==}

  es-toolkit@1.39.7:
    resolution: {integrity: sha512-ek/wWryKouBrZIjkwW2BFf91CWOIMvoy2AE5YYgUrfWsJQM2Su1LoLtrw8uusEpN9RfqLlV/0FVNjT0WMv8Bxw==}

  esbuild-register@3.6.0:
    resolution: {integrity: sha512-H2/S7Pm8a9CL1uhp9OvjwrBh5Pvx0H8qVOxNu8Wed9Y7qv56MPtq+GGM8RJpq6glYJn9Wspr8uw7l55uyinNeg==}
    peerDependencies:
      esbuild: '>=0.12 <1'

  esbuild-runner@2.2.2:
    resolution: {integrity: sha512-fRFVXcmYVmSmtYm2mL8RlUASt2TDkGh3uRcvHFOKNr/T58VrfVeKD9uT9nlgxk96u0LS0ehS/GY7Da/bXWKkhw==}
    hasBin: true
    peerDependencies:
      esbuild: '*'

  esbuild@0.25.6:
    resolution: {integrity: sha512-GVuzuUwtdsghE3ocJ9Bs8PNoF13HNQ5TXbEi2AhvVb8xU1Iwt9Fos9FEamfoee+u/TOsn7GUWc04lz46n2bbTg==}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  eslint-config-prettier@10.1.5:
    resolution: {integrity: sha512-zc1UmCpNltmVY34vuLRV61r1K27sWuX39E+uyUnY8xS2Bex88VV9cugG+UZbRSRGtGyFboj+D8JODyme1plMpw==}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-plugin-storybook@9.1.0-alpha.2:
    resolution: {integrity: sha512-OQutUbsit0iIlpPG0syit93MbJ+GzSZLWMOAVsDQbJKcfEkap5Us3oPVMZm6uM7OBKHF24ZDHCBYXHveP9fsWg==}
    engines: {node: '>=20.0.0'}
    peerDependencies:
      eslint: '>=8'
      storybook: ^9.1.0-alpha.2

  eslint-plugin-svelte@3.10.1:
    resolution: {integrity: sha512-csCh2x0ge/DugXC7dCANh46Igi7bjMZEy6rHZCdS13AoGVJSu7a90Kru3I8oMYLGEemPRE1hQXadxvRPVMAAXQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.1 || ^9.0.0
      svelte: ^3.37.0 || ^4.0.0 || ^5.0.0
    peerDependenciesMeta:
      svelte:
        optional: true

  eslint-scope@8.4.0:
    resolution: {integrity: sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@4.2.1:
    resolution: {integrity: sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint@9.30.1:
    resolution: {integrity: sha512-zmxXPNMOXmwm9E0yQLi5uqXHs7uq2UIiqEKo3Gq+3fwo1XrJ+hijAZImyF7hclW3E6oHz43Yk3RP8at6OTKflQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  esm-env@1.2.2:
    resolution: {integrity: sha512-Epxrv+Nr/CaL4ZcFGPJIYLWFom+YeV1DqMLHJoEd9SYRxNbaFruBwfEX/kkHUJf55j2+TUbmDcmuilbP1TmXHA==}

  espree@10.4.0:
    resolution: {integrity: sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true

  esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}

  esrap@1.2.2:
    resolution: {integrity: sha512-F2pSJklxx1BlQIQgooczXCPHmcWpn6EsP5oo73LQfonG9fIlIENQ8vMmfGXeojP9MrkzUNAfyU5vdFlR9shHAw==}

  esrap@1.4.9:
    resolution: {integrity: sha512-3OMlcd0a03UGuZpPeUC1HxR3nA23l+HEyCiZw3b3FumJIN9KphoGzDJKMXI1S72jVS1dsenDyQC0kJlO1U9E1g==}

  esrap@2.1.0:
    resolution: {integrity: sha512-yzmPNpl7TBbMRC5Lj2JlJZNPml0tzqoqP5B1JXycNUwtqma9AKCO0M2wHrdgsHcy1WRW7S9rJknAMtByg3usgA==}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}

  estree-walker@3.0.3:
    resolution: {integrity: sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  expect-type@1.2.2:
    resolution: {integrity: sha512-JhFGDVJ7tmDJItKhYgJCGLOWjuK9vPxiXoUFLwLDc99NlmklilbiQJwoctZtt13+xMw91MCk/REan6MWHqDjyA==}
    engines: {node: '>=12.0.0'}

  fast-check@3.23.2:
    resolution: {integrity: sha512-h5+1OzzfCC3Ef7VbtKdcv7zsstUQwUDlYpUTvjeUsJAssPgLn7QzbboPtL5ro04Mq0rPOsMzl7q5hIbRs2wD1A==}
    engines: {node: '>=8.0.0'}

  fast-deep-equal@2.0.1:
    resolution: {integrity: sha512-bCK/2Z4zLidyB4ReuIsvALH6w31YfAQDmXMqMx6FyfHqvBxtjC0eRumeSu4Bs3XtXwpyIywtSTrVT99BxY1f9w==}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-glob@3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fastq@1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==}

  fdir@6.4.6:
    resolution: {integrity: sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  fetch-blob@3.2.0:
    resolution: {integrity: sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==}
    engines: {node: ^12.20 || >= 14.13}

  file-entry-cache@8.0.0:
    resolution: {integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==}
    engines: {node: '>=16.0.0'}

  file-uri-to-path@1.0.0:
    resolution: {integrity: sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==}

  filesize@10.1.6:
    resolution: {integrity: sha512-sJslQKU2uM33qH5nqewAwVB2QgR6w1aMNsYUp3aN5rMRyXEwJGmZvaWzeJFNTOXWlHQyBFCWrdj3fV/fsTOX8w==}
    engines: {node: '>= 10.4.0'}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}

  flat-cache@4.0.1:
    resolution: {integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==}
    engines: {node: '>=16'}

  flatted@3.3.3:
    resolution: {integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==}

  foreground-child@3.3.1:
    resolution: {integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==}
    engines: {node: '>=14'}

  formdata-polyfill@4.0.10:
    resolution: {integrity: sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==}
    engines: {node: '>=12.20.0'}

  formsnap@2.0.1:
    resolution: {integrity: sha512-iJSe4YKd/W6WhLwKDVJU9FQeaJRpEFuolhju7ZXlRpUVyDdqFdMP8AUBICgnVvQPyP41IPAlBa/v0Eo35iE6wQ==}
    engines: {node: '>=18', pnpm: '>=8.7.0'}
    peerDependencies:
      svelte: ^5.0.0
      sveltekit-superforms: ^2.19.0

  forwarded-parse@2.1.2:
    resolution: {integrity: sha512-alTFZZQDKMporBH77856pXgzhEzaUVmLCDk+egLgIgHst3Tpndzz8MnKe+GzRJRfvVdn69HhpW7cmXzvtLvJAw==}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.2:
    resolution: {integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  glob@10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==}
    hasBin: true

  glob@9.3.5:
    resolution: {integrity: sha512-e1LleDykUz2Iu+MTYdkSsuWX8lvAjAcs0Xef0lNIu0S2wOAzuTxCJtcd9S3cijlwYF18EsU3rzb8jPVobxDh9Q==}
    engines: {node: '>=16 || 14 >=14.17'}

  globals@14.0.0:
    resolution: {integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==}
    engines: {node: '>=18'}

  globals@16.3.0:
    resolution: {integrity: sha512-bqWEnJ1Nt3neqx2q5SFfGS8r/ahumIakg3HcwtNlrVlwXIeNumWn/c7Pn/wKzGhf6SaW6H6uWXLqC30STCMchQ==}
    engines: {node: '>=18'}

  globalyzer@0.1.0:
    resolution: {integrity: sha512-40oNTM9UfG6aBmuKxk/giHn5nQ8RVz/SS4Ir6zgzOv9/qC3kKZ9v4etGTcJbEl/NyVQH7FGU7d+X1egr57Md2Q==}

  globrex@0.1.2:
    resolution: {integrity: sha512-uHJgbwAMwNFf5mLst7IWLNg14x1CkeqglJb/K3doi4dw6q2IvAAmM/Y81kevy83wP+Sst+nutFTYOGg3d1lsxg==}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  html-escaper@2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}

  html-to-text@9.0.5:
    resolution: {integrity: sha512-qY60FjREgVZL03vJU6IfMV4GDjGBIoOyvuFdpBDIX9yTlDw0TjxVBQp+P8NvpdIXNJvfWBTNul7fsAQJq2FNpg==}
    engines: {node: '>=14'}

  htmlparser2@8.0.2:
    resolution: {integrity: sha512-GYdjWKDkbRLkZ5geuHs5NY1puJ+PXwP7+fHPRz06Eirsb9ugf6d8kkXav6ADhcODhFFPMIXyxkxSuMf3D6NCFA==}

  https-proxy-agent@5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==}
    engines: {node: '>= 6'}

  https-proxy-agent@7.0.6:
    resolution: {integrity: sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==}
    engines: {node: '>= 14'}

  iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}

  ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}

  ignore@7.0.5:
    resolution: {integrity: sha512-Hs59xBNfUIunMFgWAbGX5cq6893IbWg4KnrjbYwX3tx0ztorVgTDA6B2sxf8ejHJ4wz8BqGUMYlnzNBer5NvGg==}
    engines: {node: '>= 4'}

  immer@10.1.1:
    resolution: {integrity: sha512-s2MPrmjovJcoMaHtx6K11Ra7oD05NT97w1IC5zpMkT6Atjr7H8LjaDd81iIxUYpMKSRRNMJE703M1Fhr/TctHw==}

  import-fresh@3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==}
    engines: {node: '>=6'}

  import-in-the-middle@1.14.2:
    resolution: {integrity: sha512-5tCuY9BV8ujfOpwtAGgsTx9CGUapcFMEEyByLv1B+v2+6DhAcw+Zr0nhQT7uwaZ7DiourxFEscghOR8e1aPLQw==}

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  indent-string@4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}

  inline-style-parser@0.2.4:
    resolution: {integrity: sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q==}

  internmap@1.0.1:
    resolution: {integrity: sha512-lDB5YccMydFBtasVtxnZ3MRBHuaoE8GKsppq+EchKL2U4nK/DmEpPHNH8MZe5HkMtpSiTSOZwfN0tzYjO/lJEw==}

  internmap@2.0.3:
    resolution: {integrity: sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==}
    engines: {node: '>=12'}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-core-module@2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==}
    engines: {node: '>= 0.4'}

  is-docker@2.2.1:
    resolution: {integrity: sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==}
    engines: {node: '>=8'}
    hasBin: true

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-reference@3.0.3:
    resolution: {integrity: sha512-ixkJoqQvAP88E6wLydLGGqCJsrFUnqoH6HnaczB8XmDH1oaWU+xxdptvikTgaEhtZ53Ky6YXiBuUI2WXLMCwjw==}

  is-wsl@2.2.0:
    resolution: {integrity: sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==}
    engines: {node: '>=8'}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  isows@1.0.7:
    resolution: {integrity: sha512-I1fSfDCZL5P0v33sVqeTDSpcstAg/N+wF5HS033mogOVIp4B+oHC7oOCsA3axAbBSGTJ8QubbNmnIRN/h8U7hg==}
    peerDependencies:
      ws: '*'

  istanbul-lib-coverage@3.2.2:
    resolution: {integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==}
    engines: {node: '>=8'}

  istanbul-lib-report@3.0.1:
    resolution: {integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==}
    engines: {node: '>=10'}

  istanbul-lib-source-maps@5.0.6:
    resolution: {integrity: sha512-yg2d+Em4KizZC5niWhQaIomgf5WlL4vOOjZ5xGCmF8SnPE/mDWWXgvRExdcpCgh9lLRRa1/fSYp2ymmbJ1pI+A==}
    engines: {node: '>=10'}

  istanbul-reports@3.1.7:
    resolution: {integrity: sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==}
    engines: {node: '>=8'}

  jackspeak@3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==}

  jiti@2.4.2:
    resolution: {integrity: sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==}
    hasBin: true

  joi@17.13.3:
    resolution: {integrity: sha512-otDA4ldcIx+ZXsKHWmp0YizCweVRZG96J10b0FevjfuncLO1oX59THoAmHkNubYJ+9gWsYsp5k8v4ib6oDv1fA==}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-tokens@9.0.1:
    resolution: {integrity: sha512-mxa9E9ITFOt0ban3j6L5MpjwegGz6lBQmM1IJkWeBZGcMxto50+eWdjC/52xDbS2vy0k7vIMK0Fe2wfL9OQSpQ==}

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}

  json-schema-to-ts@3.1.1:
    resolution: {integrity: sha512-+DWg8jCJG2TEnpy7kOm/7/AxaYoaRbjVB4LFZLySZlWn8exGs3A4OLJR966cVvU26N7X9TWxl+Jsw7dzAqKT6g==}
    engines: {node: '>=16'}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}

  jsox@1.2.123:
    resolution: {integrity: sha512-LYordXJ/0Q4G8pUE1Pvh4fkfGvZY7lRe4WIJKl0wr0rtFDVw9lcdNW95GH0DceJ6E9xh41zJNW0vreEz7xOxCw==}
    hasBin: true

  keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}

  kleur@3.0.3:
    resolution: {integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==}
    engines: {node: '>=6'}

  kleur@4.1.5:
    resolution: {integrity: sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==}
    engines: {node: '>=6'}

  known-css-properties@0.37.0:
    resolution: {integrity: sha512-JCDrsP4Z1Sb9JwG0aJ8Eo2r7k4Ou5MwmThS/6lcIe1ICyb7UBJKGRIUUdqc2ASdE/42lgz6zFUnzAIhtXnBVrQ==}

  layerchart@2.0.0-next.6:
    resolution: {integrity: sha512-QGYhFp3PwZlDzl5b6zNFwFeqROWS0wPyq6ce+9rrETaoRfrmlf3JlJmpLmt9Al8jkwZIQiE9GSQ4phWbW/nTOQ==}
    peerDependencies:
      svelte: ^5.0.0

  leac@0.6.0:
    resolution: {integrity: sha512-y+SqErxb8h7nE/fiEX07jsbuhrpO9lL8eca7/Y1nuWV2moNlXhyd59iDGcRf6moVyDMbmTNzL40SUyrFU/yDpg==}

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  libphonenumber-js@1.12.9:
    resolution: {integrity: sha512-VWwAdNeJgN7jFOD+wN4qx83DTPMVPPAUyx9/TUkBXKLiNkuWWk6anV0439tgdtwaJDrEdqkvdN22iA6J4bUCZg==}

  lightningcss-darwin-arm64@1.30.1:
    resolution: {integrity: sha512-c8JK7hyE65X1MHMN+Viq9n11RRC7hgin3HhYKhrMyaXflk5GVplZ60IxyoVtzILeKr+xAJwg6zK6sjTBJ0FKYQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [darwin]

  lightningcss-darwin-x64@1.30.1:
    resolution: {integrity: sha512-k1EvjakfumAQoTfcXUcHQZhSpLlkAuEkdMBsI/ivWw9hL+7FtilQc0Cy3hrx0AAQrVtQAbMI7YjCgYgvn37PzA==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [darwin]

  lightningcss-freebsd-x64@1.30.1:
    resolution: {integrity: sha512-kmW6UGCGg2PcyUE59K5r0kWfKPAVy4SltVeut+umLCFoJ53RdCUWxcRDzO1eTaxf/7Q2H7LTquFHPL5R+Gjyig==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [freebsd]

  lightningcss-linux-arm-gnueabihf@1.30.1:
    resolution: {integrity: sha512-MjxUShl1v8pit+6D/zSPq9S9dQ2NPFSQwGvxBCYaBYLPlCWuPh9/t1MRS8iUaR8i+a6w7aps+B4N0S1TYP/R+Q==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm]
    os: [linux]

  lightningcss-linux-arm64-gnu@1.30.1:
    resolution: {integrity: sha512-gB72maP8rmrKsnKYy8XUuXi/4OctJiuQjcuqWNlJQ6jZiWqtPvqFziskH3hnajfvKB27ynbVCucKSm2rkQp4Bw==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-arm64-musl@1.30.1:
    resolution: {integrity: sha512-jmUQVx4331m6LIX+0wUhBbmMX7TCfjF5FoOH6SD1CttzuYlGNVpA7QnrmLxrsub43ClTINfGSYyHe2HWeLl5CQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-x64-gnu@1.30.1:
    resolution: {integrity: sha512-piWx3z4wN8J8z3+O5kO74+yr6ze/dKmPnI7vLqfSqI8bccaTGY5xiSGVIJBDd5K5BHlvVLpUB3S2YCfelyJ1bw==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]

  lightningcss-linux-x64-musl@1.30.1:
    resolution: {integrity: sha512-rRomAK7eIkL+tHY0YPxbc5Dra2gXlI63HL+v1Pdi1a3sC+tJTcFrHX+E86sulgAXeI7rSzDYhPSeHHjqFhqfeQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]

  lightningcss-win32-arm64-msvc@1.30.1:
    resolution: {integrity: sha512-mSL4rqPi4iXq5YVqzSsJgMVFENoa4nGTT/GjO2c0Yl9OuQfPsIfncvLrEW6RbbB24WtZ3xP/2CCmI3tNkNV4oA==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [win32]

  lightningcss-win32-x64-msvc@1.30.1:
    resolution: {integrity: sha512-PVqXh48wh4T53F/1CCu8PIPCxLzWyCnn/9T5W1Jpmdy5h9Cwd+0YQS6/LwhHXSafuc61/xg9Lv5OrCby6a++jg==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [win32]

  lightningcss@1.30.1:
    resolution: {integrity: sha512-xi6IyHML+c9+Q3W0S4fCQJOym42pyurFiJUHEcEyHS0CeKzia4yZDEsLlqOFykxOdHpNy0NmvVO31vcSqAxJCg==}
    engines: {node: '>= 12.0.0'}

  lilconfig@2.1.0:
    resolution: {integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==}
    engines: {node: '>=10'}

  locate-character@3.0.0:
    resolution: {integrity: sha512-SW13ws7BjaeJ6p7Q6CO2nchbYEc3X3J6WrmTTDto7yMPqVSZTUyY5Tjbid+Ab8gLnATtygYtiDIJGQRRn2ZOiA==}

  locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}

  lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}

  lodash.castarray@4.4.0:
    resolution: {integrity: sha512-aVx8ztPv7/2ULbArGJ2Y42bG1mEQ5mGjpdvrbJcJFU3TbYybe+QlLS4pst9zV52ymy2in1KpFPiZnAOATxD4+Q==}

  lodash.isplainobject@4.0.6:
    resolution: {integrity: sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  loupe@3.1.4:
    resolution: {integrity: sha512-wJzkKwJrheKtknCOKNEtDK4iqg/MxmZheEMtSTYvnzRdEYaZzmgH976nenp8WdJRdx5Vc1X/9MO0Oszl6ezeXg==}

  lower-case@2.0.2:
    resolution: {integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==}

  lru-cache@10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  lz-string@1.5.0:
    resolution: {integrity: sha512-h5bgJWpxJNswbU7qCrV0tIKQCaS3blPDrqKWx+QxzuzL1zGUzij9XCWLrSLsJPu5t+eWA/ycetzYAO5IOMcWAQ==}
    hasBin: true

  magic-string@0.30.17:
    resolution: {integrity: sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==}

  magic-string@0.30.7:
    resolution: {integrity: sha512-8vBuFF/I/+OSLRmdf2wwFCJCz+nSn0m6DPvGH1fS/KiQoSaR+sETbov0eIk9KhEKy8CYqIkIAnbohxT/4H0kuA==}
    engines: {node: '>=12'}

  magic-string@0.30.8:
    resolution: {integrity: sha512-ISQTe55T2ao7XtlAStud6qwYPZjE4GK1S/BeVPus4jrq6JuOnQ00YKQC581RWhR122W7msZV263KzVeLoqidyQ==}
    engines: {node: '>=12'}

  magicast@0.3.5:
    resolution: {integrity: sha512-L0WhttDl+2BOsybvEOLK7fW3UA0OQ0IQ2d6Zl2x/a6vVRs3bAY0ECOSHHeL5jD+SbOpOCUEi0y1DgHEn9Qn1AQ==}

  make-dir@4.0.0:
    resolution: {integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==}
    engines: {node: '>=10'}

  memoize-weak@1.0.2:
    resolution: {integrity: sha512-gj39xkrjEw7nCn4nJ1M5ms6+MyMlyiGmttzsqAUsAKn6bYKwuTHh/AO3cKPF8IBrTIYTxb0wWXFs3E//Y8VoWQ==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  min-indent@1.0.1:
    resolution: {integrity: sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==}
    engines: {node: '>=4'}

  mini-svg-data-uri@1.4.4:
    resolution: {integrity: sha512-r9deDe9p5FJUPZAk3A59wGH7Ii9YrjjWw0jmw/liSbHl2CHiyXj6FcDXDu2K3TjVAXqiJdaw3xxwlZZr9E6nHg==}
    hasBin: true

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@8.0.4:
    resolution: {integrity: sha512-W0Wvr9HyFXZRGIDgCicunpQ299OKXs9RgZfaukz4qAW/pJhcpUfupc9c+OObPOFueNy8VSrZgEmDtk6Kh4WzDA==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  minipass@4.2.8:
    resolution: {integrity: sha512-fNzuVyifolSLFL4NzpF+wEF4qrgqaaKX0haXPQEdQ7NKAN+WecoKMHV09YcuL/DHxrUsYQOK3MiuDf7Ip2OXfQ==}
    engines: {node: '>=8'}

  minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}

  minizlib@3.0.2:
    resolution: {integrity: sha512-oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA==}
    engines: {node: '>= 18'}

  mkdirp@3.0.1:
    resolution: {integrity: sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg==}
    engines: {node: '>=10'}
    hasBin: true

  mode-watcher@1.1.0:
    resolution: {integrity: sha512-mUT9RRGPDYenk59qJauN1rhsIMKBmWA3xMF+uRwE8MW/tjhaDSCCARqkSuDTq8vr4/2KcAxIGVjACxTjdk5C3g==}
    peerDependencies:
      svelte: ^5.27.0

  module-details-from-path@1.0.4:
    resolution: {integrity: sha512-EGWKgxALGMgzvxYF1UyGTy0HXX/2vHLkw6+NvDKW2jypWbHpjQuj4UMcqQWXHERJhVGKikolT06G3bcKe4fi7w==}

  moo@0.5.2:
    resolution: {integrity: sha512-iSAJLHYKnX41mKcJKjqvnAN9sf0LMDTXDEvFv+ffuRR9a1MIuXLjMNL6EsnDHSkKLTWNqQQ5uo61P4EbU4NU+Q==}

  mri@1.2.0:
    resolution: {integrity: sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA==}
    engines: {node: '>=4'}

  mrmime@2.0.1:
    resolution: {integrity: sha512-Y3wQdFg2Va6etvQ5I82yUhGdsKrcYox6p7FfL1LbK2J4V01F9TGlepTIhnK24t7koZibmg82KGglhA1XK5IsLQ==}
    engines: {node: '>=10'}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  nearley@2.20.1:
    resolution: {integrity: sha512-+Mc8UaAebFzgV+KpI5n7DasuuQCHA89dmwm7JXw3TV43ukfNQ9DnBH3Mdb2g/I4Fdxc26pwimBWvjIw0UAILSQ==}
    hasBin: true

  no-case@3.0.4:
    resolution: {integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==}

  node-domexception@1.0.0:
    resolution: {integrity: sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==}
    engines: {node: '>=10.5.0'}
    deprecated: Use your platform's native DOMException instead

  node-fetch@2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-fetch@3.3.2:
    resolution: {integrity: sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  node-gyp-build@4.8.4:
    resolution: {integrity: sha512-LA4ZjwlnUblHVgq0oBF3Jl/6h/Nvs5fzBLwdEF4nuxnFdsfajde4WfxtJr3CaiH+F6ewcIB/q4jQ4UzPyid+CQ==}
    hasBin: true

  node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}

  node-sql-parser@5.3.10:
    resolution: {integrity: sha512-cf+iXXJ9Foz4hBIu+eNNeg207ac6XruA9I9DXEs+jCxeS9t/k9T0GZK8NZngPwkv+P26i3zNFj9jxJU2v3pJnw==}
    engines: {node: '>=8'}

  nopt@8.1.0:
    resolution: {integrity: sha512-ieGu42u/Qsa4TFktmaKEwM6MQH0pOWnaB3htzh0JRtx84+Mebc0cbZYN5bC+6WTZ4+77xrL9Pn5m7CV6VIkV7A==}
    engines: {node: ^18.17.0 || >=20.5.0}
    hasBin: true

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  normalize-url@8.0.2:
    resolution: {integrity: sha512-Ee/R3SyN4BuynXcnTaekmaVdbDAEiNrHqjQIA37mHU8G9pf7aaAD4ZX3XjBLo6rsdcxA/gtkcNYZLt30ACgynw==}
    engines: {node: '>=14.16'}

  npm-normalize-package-bin@4.0.0:
    resolution: {integrity: sha512-TZKxPvItzai9kN9H/TkmCtx/ZN/hvr3vUycjlfmH0ootY9yFBzNOpiXAdIn1Iteqsvk4lQn6B5PTrt+n6h8k/w==}
    engines: {node: ^18.17.0 || >=20.5.0}

  open@8.4.2:
    resolution: {integrity: sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==}
    engines: {node: '>=12'}

  optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}

  p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}

  p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}

  package-json-from-dist@1.0.1:
    resolution: {integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==}

  paneforge@1.0.0:
    resolution: {integrity: sha512-VlE5+inlTtfguYzSXCWlgFOrt92ofMkwm3/9dwwaDxABAJYYPOS6OJL+qNQbMr6RZkh7y1GSmB5sN+UG7EmVBA==}
    peerDependencies:
      svelte: ^5.29.0

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parseley@0.12.1:
    resolution: {integrity: sha512-e6qHKe3a9HWr0oMRVDTRhKce+bRO8VGQR3NyVwcjwrbhMmFCX9KszEV35+rn4AdilFAq9VPxP/Fe1wC9Qjd2lw==}

  pascal-case@3.1.2:
    resolution: {integrity: sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}

  pathe@2.0.3:
    resolution: {integrity: sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==}

  pathval@2.0.1:
    resolution: {integrity: sha512-//nshmD55c46FuFw26xV/xFAaB5HF9Xdap7HJBBnrKdAd6/GxDBaNA1870O79+9ueg61cZLSVc+OaFlfmObYVQ==}
    engines: {node: '>= 14.16'}

  peberminta@0.9.0:
    resolution: {integrity: sha512-XIxfHpEuSJbITd1H3EeQwpcZbTLHc+VVr8ANI9t5sit565tsI4/xK3KWTUFE2e6QiangUkh3B0jihzmGnNrRsQ==}

  pg-int8@1.0.1:
    resolution: {integrity: sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw==}
    engines: {node: '>=4.0.0'}

  pg-protocol@1.10.3:
    resolution: {integrity: sha512-6DIBgBQaTKDJyxnXaLiLR8wBpQQcGWuAESkRBX/t6OwA8YsqP+iVSiond2EDy6Y/dsGk8rh/jtax3js5NeV7JQ==}

  pg-types@2.2.0:
    resolution: {integrity: sha512-qTAAlrEsl8s4OiEQY69wDvcMIdQN6wdz5ojQiOy6YRMuynxenON0O5oCpJI6lshc6scgAY8qvJ2On/p+CXY0GA==}
    engines: {node: '>=4'}

  phosphor-svelte@3.0.1:
    resolution: {integrity: sha512-QuxdzWCBjNZXglk4XpmaKb+JRA6XgKOYWpBY8k82NZwxO5ec7u2iFuueAijKYgj/7hLWHtreAPc2Q5uZFNTRSQ==}
    peerDependencies:
      svelte: ^5.0.0 || ^5.0.0-next.96
      vite: '>=5'
    peerDependenciesMeta:
      vite:
        optional: true

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  picomatch@4.0.2:
    resolution: {integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==}
    engines: {node: '>=12'}

  playwright-core@1.53.2:
    resolution: {integrity: sha512-ox/OytMy+2w1jcYEYlOo1Hhp8hZkLCximMTUTMBXjGUA1KoFfiSZ+DU+3a739jsPY0yoKH2TFy9S2fsJas8yAw==}
    engines: {node: '>=18'}
    hasBin: true

  playwright@1.53.2:
    resolution: {integrity: sha512-6K/qQxVFuVQhRQhFsVZ9fGeatxirtrpPgxzBYWyZLEXJzqYwuL4fuNmfOfD5et1tJE4GScKyPNeLhZeRwuTU3A==}
    engines: {node: '>=18'}
    hasBin: true

  postcss-load-config@3.1.4:
    resolution: {integrity: sha512-6DiM4E7v4coTE4uzA8U//WhtPwyhiim3eyjEMFCnUpzbrkK9wJHgKDT2mR+HbtSrd/NubVaYTOpSpjUl8NQeRg==}
    engines: {node: '>= 10'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-safe-parser@7.0.1:
    resolution: {integrity: sha512-0AioNCJZ2DPYz5ABT6bddIqlhgwhpHZ/l65YAYo0BCIn0xiDpsnTHz0gnoTGk0OXZW0JRs+cDwL8u/teRdz+8A==}
    engines: {node: '>=18.0'}
    peerDependencies:
      postcss: ^8.4.31

  postcss-scss@4.0.9:
    resolution: {integrity: sha512-AjKOeiwAitL/MXxQW2DliT28EKukvvbEWx3LBmJIRN8KfBGZbRTxNYW0kSqi1COiTZ57nZ9NW06S6ux//N1c9A==}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.4.29

  postcss-selector-parser@6.0.10:
    resolution: {integrity: sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==}
    engines: {node: '>=4'}

  postcss-selector-parser@7.1.0:
    resolution: {integrity: sha512-8sLjZwK0R+JlxlYcTuVnyT2v+htpdrjDOKuMcOVdYjt52Lh8hWRYpxBPoKx/Zg+bcjc3wx6fmQevMmUztS/ccA==}
    engines: {node: '>=4'}

  postcss@8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==}
    engines: {node: ^10 || ^12 || >=14}

  postgres-array@2.0.0:
    resolution: {integrity: sha512-VpZrUqU5A69eQyW2c5CA1jtLecCsN2U/bD6VilrFDWq5+5UIEVO7nazS3TEcHf1zuPYO/sqGvUvW62g86RXZuA==}
    engines: {node: '>=4'}

  postgres-bytea@1.0.0:
    resolution: {integrity: sha512-xy3pmLuQqRBZBXDULy7KbaitYqLcmxigw14Q5sj8QBVLqEwXfeybIKVWiqAXTlcvdvb0+xkOtDbfQMOf4lST1w==}
    engines: {node: '>=0.10.0'}

  postgres-date@1.0.7:
    resolution: {integrity: sha512-suDmjLVQg78nMK2UZ454hAG+OAW+HQPZ6n++TNDUX+L0+uUlLywnoxJKDou51Zm+zTCjrCl0Nq6J9C5hP9vK/Q==}
    engines: {node: '>=0.10.0'}

  postgres-interval@1.2.0:
    resolution: {integrity: sha512-9ZhXKM/rw350N1ovuWHbGxnGh/SNJ4cnxHiM0rxE4VN41wsg8P8zWn9hv/buK00RP4WvlOyr/RBDiptyxVbkZQ==}
    engines: {node: '>=0.10.0'}

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  prettier-plugin-sql@0.19.1:
    resolution: {integrity: sha512-Kvxs8uRBdwT64dg/+pjZN1dojLTfZjqtRss8jbUVLqGPXRDp0vJuPkR7otzyQz5no6jSkt6TGFvC80aOnXlpXw==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      prettier: ^3.0.3

  prettier-plugin-svelte@3.4.0:
    resolution: {integrity: sha512-pn1ra/0mPObzqoIQn/vUTR3ZZI6UuZ0sHqMK5x2jMLGrs53h0sXhkVuDcrlssHwIMk7FYrMjHBPoUSyyEEDlBQ==}
    peerDependencies:
      prettier: ^3.0.0
      svelte: ^3.2.0 || ^4.0.0-next.0 || ^5.0.0-next.0

  prettier-plugin-tailwindcss@0.6.13:
    resolution: {integrity: sha512-uQ0asli1+ic8xrrSmIOaElDu0FacR4x69GynTh2oZjFY10JUt6EEumTQl5tB4fMeD6I1naKd+4rXQQ7esT2i1g==}
    engines: {node: '>=14.21.3'}
    peerDependencies:
      '@ianvs/prettier-plugin-sort-imports': '*'
      '@prettier/plugin-pug': '*'
      '@shopify/prettier-plugin-liquid': '*'
      '@trivago/prettier-plugin-sort-imports': '*'
      '@zackad/prettier-plugin-twig': '*'
      prettier: ^3.0
      prettier-plugin-astro: '*'
      prettier-plugin-css-order: '*'
      prettier-plugin-import-sort: '*'
      prettier-plugin-jsdoc: '*'
      prettier-plugin-marko: '*'
      prettier-plugin-multiline-arrays: '*'
      prettier-plugin-organize-attributes: '*'
      prettier-plugin-organize-imports: '*'
      prettier-plugin-sort-imports: '*'
      prettier-plugin-style-order: '*'
      prettier-plugin-svelte: '*'
    peerDependenciesMeta:
      '@ianvs/prettier-plugin-sort-imports':
        optional: true
      '@prettier/plugin-pug':
        optional: true
      '@shopify/prettier-plugin-liquid':
        optional: true
      '@trivago/prettier-plugin-sort-imports':
        optional: true
      '@zackad/prettier-plugin-twig':
        optional: true
      prettier-plugin-astro:
        optional: true
      prettier-plugin-css-order:
        optional: true
      prettier-plugin-import-sort:
        optional: true
      prettier-plugin-jsdoc:
        optional: true
      prettier-plugin-marko:
        optional: true
      prettier-plugin-multiline-arrays:
        optional: true
      prettier-plugin-organize-attributes:
        optional: true
      prettier-plugin-organize-imports:
        optional: true
      prettier-plugin-sort-imports:
        optional: true
      prettier-plugin-style-order:
        optional: true
      prettier-plugin-svelte:
        optional: true

  prettier@3.6.2:
    resolution: {integrity: sha512-I7AIg5boAr5R0FFtJ6rCfD+LFsWHp81dolrFD8S79U9tb8Az2nGrJncnMSnys+bpQJfRUzqs9hnA81OAA3hCuQ==}
    engines: {node: '>=14'}
    hasBin: true

  pretty-format@27.5.1:
    resolution: {integrity: sha512-Qb1gy5OrP5+zDf2Bvnzdl3jsTf1qXVMazbvCoKhtKqVs4/YK4ozX4gKQJJVyNe+cajNPn0KoC0MC3FUmaHWEmQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  pretty-quick@4.2.2:
    resolution: {integrity: sha512-uAh96tBW1SsD34VhhDmWuEmqbpfYc/B3j++5MC/6b3Cb8Ow7NJsvKFhg0eoGu2xXX+o9RkahkTK6sUdd8E7g5w==}
    engines: {node: '>=14'}
    hasBin: true
    peerDependencies:
      prettier: ^3.0.0

  proc-log@5.0.0:
    resolution: {integrity: sha512-Azwzvl90HaF0aCz1JrDdXQykFakSSNPaPoiZ9fm5qJIMHioDZEi7OAdRwSm6rSoPtY3Qutnm3L7ogmg3dc+wbQ==}
    engines: {node: ^18.17.0 || >=20.5.0}

  progress@2.0.3:
    resolution: {integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==}
    engines: {node: '>=0.4.0'}

  prompts@2.4.2:
    resolution: {integrity: sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==}
    engines: {node: '>= 6'}

  property-expr@2.0.6:
    resolution: {integrity: sha512-SVtmxhRE/CGkn3eZY1T6pC8Nln6Fr/lu1mKSgRud0eC73whjGfoAogbn78LkD8aFL0zz3bAFerKSnOl7NlErBA==}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  pure-rand@6.1.0:
    resolution: {integrity: sha512-bVWawvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  railroad-diagrams@1.0.0:
    resolution: {integrity: sha512-cz93DjNeLY0idrCNOH6PviZGRN9GJhsdm9hpn1YCS879fj4W+x5IFJhhkRZcwVgMmFF7R82UA/7Oh+R8lLZg6A==}

  randexp@0.4.6:
    resolution: {integrity: sha512-80WNmd9DA0tmZrw9qQa62GPPWfuXJknrmVmLcxvq4uZBdYqb1wYoKTmnlGUchvVWe0XiLupYkBoXVOxz3C8DYQ==}
    engines: {node: '>=0.12'}

  react-dom@19.1.0:
    resolution: {integrity: sha512-Xs1hdnE+DyKgeHJeJznQmYMIBG3TKIHJJT95Q58nHLSrElKlGQqDTR2HQ9fx5CN/Gk6Vh/kupBTDLU11/nDk/g==}
    peerDependencies:
      react: ^19.1.0

  react-is@17.0.2:
    resolution: {integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==}

  react-promise-suspense@0.3.4:
    resolution: {integrity: sha512-I42jl7L3Ze6kZaq+7zXWSunBa3b1on5yfvUW6Eo/3fFOj6dZ5Bqmcd264nJbTK/gn1HjjILAjSwnZbV4RpSaNQ==}

  react@19.1.0:
    resolution: {integrity: sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg==}
    engines: {node: '>=0.10.0'}

  read-cmd-shim@5.0.0:
    resolution: {integrity: sha512-SEbJV7tohp3DAAILbEMPXavBjAnMN0tVnh4+9G8ihV4Pq3HYF9h8QNez9zkJ1ILkv9G2BjdzwctznGZXgu/HGw==}
    engines: {node: ^18.17.0 || >=20.5.0}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  readdirp@4.1.2:
    resolution: {integrity: sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==}
    engines: {node: '>= 14.18.0'}

  recast@0.23.11:
    resolution: {integrity: sha512-YTUo+Flmw4ZXiWfQKGcwwc11KnoRAYgzAE2E7mXKCjSviTKShtxBsN6YUUBB2gtaBzKzeKunxhUwNHQuRryhWA==}
    engines: {node: '>= 4'}

  redent@3.0.0:
    resolution: {integrity: sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==}
    engines: {node: '>=8'}

  require-in-the-middle@7.5.2:
    resolution: {integrity: sha512-gAZ+kLqBdHarXB64XpAe2VCjB7rIRv+mU8tfRWziHRJ5umKsIHN2tLLv6EtMw7WCdP19S0ERVMldNvxYCHnhSQ==}
    engines: {node: '>=8.6.0'}

  resend@4.6.0:
    resolution: {integrity: sha512-D5T2I82FvEUYFlrHzaDvVtr5ADHdhuoLaXgLFGABKyNtQgPWIuz0Vp2L2Evx779qjK37aF4kcw1yXJDHhA2JnQ==}
    engines: {node: '>=18'}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve-from@5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}

  resolve@1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==}
    engines: {node: '>= 0.4'}
    hasBin: true

  ret@0.1.15:
    resolution: {integrity: sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==}
    engines: {node: '>=0.12'}

  reusify@1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  robust-predicates@3.0.2:
    resolution: {integrity: sha512-IXgzBWvWQwE6PrDI05OvmXUIruQTcoMDzRsOd5CDvHCVLcLHMTSYvOK5Cm46kWqlV3yAbuSpBZdJ5oP5OUoStg==}

  rollup@4.44.2:
    resolution: {integrity: sha512-PVoapzTwSEcelaWGth3uR66u7ZRo6qhPHc0f2uRO9fX6XDVNrIiGYS0Pj9+R8yIIYSD/mCx2b16Ws9itljKSPg==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  runed@0.23.4:
    resolution: {integrity: sha512-9q8oUiBYeXIDLWNK5DfCWlkL0EW3oGbk845VdKlPeia28l751VpfesaB/+7pI6rnbx1I6rqoZ2fZxptOJLxILA==}
    peerDependencies:
      svelte: ^5.7.0

  runed@0.25.0:
    resolution: {integrity: sha512-7+ma4AG9FT2sWQEA0Egf6mb7PBT2vHyuHail1ie8ropfSjvZGtEAx8YTmUjv/APCsdRRxEVvArNjALk9zFSOrg==}
    peerDependencies:
      svelte: ^5.7.0

  runed@0.27.0:
    resolution: {integrity: sha512-Iwn06Yj7tOnN8UKaSYxY1m7lqig03ZUOlZnZJJ6jDvBgcaxSlrDoWL2v/14yvoE8xFdI9ljdut3DwvzpZbz3/w==}
    peerDependencies:
      svelte: ^5.7.0

  runed@0.28.0:
    resolution: {integrity: sha512-k2xx7RuO9hWcdd9f+8JoBeqWtYrm5CALfgpkg2YDB80ds/QE4w0qqu34A7fqiAwiBBSBQOid7TLxwxVC27ymWQ==}
    peerDependencies:
      svelte: ^5.7.0

  runed@0.29.2:
    resolution: {integrity: sha512-0cq6cA6sYGZwl/FvVqjx9YN+1xEBu9sDDyuWdDW1yWX7JF2wmvmVKfH+hVCZs+csW+P3ARH92MjI3H9QTagOQA==}
    peerDependencies:
      svelte: ^5.7.0

  rw@1.3.3:
    resolution: {integrity: sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ==}

  sade@1.8.1:
    resolution: {integrity: sha512-xal3CZX1Xlo/k4ApwCFrHVACi9fBqJ7V+mwhBsuf/1IOKbBy098Fex+Wa/5QMubw09pSZ/u8EY8PWgevJsXp1A==}
    engines: {node: '>=6'}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  scheduler@0.26.0:
    resolution: {integrity: sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA==}

  selderee@0.11.0:
    resolution: {integrity: sha512-5TF+l7p4+OsnP8BCCvSyZiSPc4x4//p5uPwK8TCnVPJYRmU2aYKMpOXvw8zM5a5JvuuCGN1jmsMwuU2W02ukfA==}

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  set-cookie-parser@2.7.1:
    resolution: {integrity: sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ==}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  shimmer@1.2.1:
    resolution: {integrity: sha512-sQTKC1Re/rM6XyFM6fIAGHRPVGvyXfgzIDvzoq608vM+jeyVD0Tu1E6Np0Kc2zAIFWIj963V2800iF/9LPieQw==}

  siginfo@2.0.0:
    resolution: {integrity: sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g==}

  signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  simple-git-hooks@2.13.0:
    resolution: {integrity: sha512-N+goiLxlkHJlyaYEglFypzVNMaNplPAk5syu0+OPp/Bk6dwVoXF6FfOw2vO0Dp+JHsBaI+w6cm8TnFl2Hw6tDA==}
    hasBin: true

  sirv@3.0.1:
    resolution: {integrity: sha512-FoqMu0NCGBLCcAkS1qA+XJIQTR6/JHfQXl+uGteNCQ76T91DMUjPa9xfmeqMY3z80nLSg9yQmNjK0Px6RWsH/A==}
    engines: {node: '>=18'}

  sisteransi@1.0.5:
    resolution: {integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==}

  sorcery@1.0.0:
    resolution: {integrity: sha512-5ay9oJE+7sNmhzl3YNG18jEEEf4AOQCM/FAqR5wMmzqd1FtRorFbJXn3w3SKOhbiQaVgHM+Q1lszZspjri7bpA==}
    hasBin: true

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  sql-formatter@15.6.6:
    resolution: {integrity: sha512-bZydXEXhaNDQBr8xYHC3a8thwcaMuTBp0CkKGjwGYDsIB26tnlWeWPwJtSQ0TEwiJcz9iJJON5mFPkx7XroHcg==}
    hasBin: true

  stackback@0.0.2:
    resolution: {integrity: sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw==}

  std-env@3.9.0:
    resolution: {integrity: sha512-UGvjygr6F6tpH7o2qyqR6QYpwraIjKSdtzyBdyytFOHmPZY917kwdwLG0RbOjWOnKmnm3PeHjaoLLMie7kPLQw==}

  storybook@9.1.0-alpha.2:
    resolution: {integrity: sha512-81A4Dx6pyBzmmxeWVTEf+Epr4Y8UVv8cKWxzwYlKbkhLWzYtxG+Md9ZLyvJnjl9x6kkMEpJSIkyv2c6OFcQIrQ==}
    hasBin: true
    peerDependencies:
      prettier: ^2 || ^3
    peerDependenciesMeta:
      prettier:
        optional: true

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}

  strip-indent@3.0.0:
    resolution: {integrity: sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==}
    engines: {node: '>=8'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  strip-literal@3.0.0:
    resolution: {integrity: sha512-TcccoMhJOM3OebGhSBEmp3UZ2SfDMZUEBdRA/9ynfLi8yYajyWX3JiXArcJt4Umh4vISpspkQIY8ZZoCqjbviA==}

  style-to-object@1.0.9:
    resolution: {integrity: sha512-G4qppLgKu/k6FwRpHiGiKPaPTFcG3g4wNVX/Qsfu+RqQM30E7Tyu/TEgxcL9PNLF5pdRLwQdE3YKKf+KF2Dzlw==}

  supabase@2.30.4:
    resolution: {integrity: sha512-AOCyd2vmBBMTXbnahiCU0reRNxKS4n5CrPciUF2tcTrQ8dLzl1HwcLfe5DrG8E0QRcKHPDdzprmh/2+y4Ta5MA==}
    engines: {npm: '>=8'}
    hasBin: true

  superstruct@2.0.2:
    resolution: {integrity: sha512-uV+TFRZdXsqXTL2pRvujROjdZQ4RAlBUS5BTh9IGm+jTqQntYThciG/qu57Gs69yjnVUSqdxF9YLmSnpupBW9A==}
    engines: {node: '>=14.0.0'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  svelte-ast-print@0.4.2:
    resolution: {integrity: sha512-hRHHufbJoArFmDYQKCpCvc0xUuIEfwYksvyLYEQyH+1xb5LD5sM/IthfooCdXZQtOIqXz6xm7NmaqdfwG4kh6w==}
    engines: {node: '>=18'}
    peerDependencies:
      svelte: ^5.0.0

  svelte-check@4.2.2:
    resolution: {integrity: sha512-1+31EOYZ7NKN0YDMKusav2hhEoA51GD9Ws6o//0SphMT0ve9mBTsTUEX7OmDMadUP3KjNHsSKtJrqdSaD8CrGQ==}
    engines: {node: '>= 18.0.0'}
    hasBin: true
    peerDependencies:
      svelte: ^4.0.0 || ^5.0.0-next.0
      typescript: '>=5.0.0'

  svelte-eslint-parser@1.2.0:
    resolution: {integrity: sha512-mbPtajIeuiyU80BEyGvwAktBeTX7KCr5/0l+uRGLq1dafwRNrjfM5kHGJScEBlPG3ipu6dJqfW/k0/fujvIEVw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      svelte: ^3.37.0 || ^4.0.0 || ^5.0.0
    peerDependenciesMeta:
      svelte:
        optional: true

  svelte-sonner@1.0.5:
    resolution: {integrity: sha512-9dpGPFqKb/QWudYqGnEz93vuY+NgCEvyNvxoCLMVGw6sDN/3oVeKV1xiEirW2E1N3vJEyj5imSBNOGltQHA7mg==}
    peerDependencies:
      svelte: ^5.0.0

  svelte-toolbelt@0.5.0:
    resolution: {integrity: sha512-t3tenZcnfQoIeRuQf/jBU7bvTeT3TGkcEE+1EUr5orp0lR7NEpprflpuie3x9Dn0W9nOKqs3HwKGJeeN5Ok1sQ==}
    engines: {node: '>=18', pnpm: '>=8.7.0'}
    peerDependencies:
      svelte: ^5.0.0-next.126

  svelte-toolbelt@0.7.1:
    resolution: {integrity: sha512-HcBOcR17Vx9bjaOceUvxkY3nGmbBmCBBbuWLLEWO6jtmWH8f/QoWmbyUfQZrpDINH39en1b8mptfPQT9VKQ1xQ==}
    engines: {node: '>=18', pnpm: '>=8.7.0'}
    peerDependencies:
      svelte: ^5.0.0

  svelte-toolbelt@0.9.3:
    resolution: {integrity: sha512-HCSWxCtVmv+c6g1ACb8LTwHVbDqLKJvHpo6J8TaqwUme2hj9ATJCpjCPNISR1OCq2Q4U1KT41if9ON0isINQZw==}
    engines: {node: '>=18', pnpm: '>=8.7.0'}
    peerDependencies:
      svelte: ^5.30.2

  svelte2tsx@0.7.40:
    resolution: {integrity: sha512-Fgqe2lzC9DWT/kQTIXqN39O2ot9rUqzUu9dqpbuI6EsaEJ6+RSXVmXnxcNYMlKb2LRPDoIg9TVzXYWwi0zhCmQ==}
    peerDependencies:
      svelte: ^3.55 || ^4.0.0-next.0 || ^4.0 || ^5.0.0-next.0
      typescript: ^4.9.4 || ^5.0.0

  svelte@5.35.4:
    resolution: {integrity: sha512-NUUD+GcV/uvLBANoFwPNtnlkJM77PEkYYH6TChRZnGI1a5UHc9k2Glq7jxGtClfVz2ZhEvpg+c4yS577qM1c6g==}
    engines: {node: '>=18'}

  sveltekit-flash-message@2.4.6:
    resolution: {integrity: sha512-++FGMWiKbQyJn133xwyTAKzoW7NPtsa3BrYq33IlR7Efh2J0XvLXg6rZkAROM8eKBG9s2MoxDANFfxa6pOxS6A==}
    peerDependencies:
      '@sveltejs/kit': 1.x || 2.x
      svelte: 3.x || 4.x || >=5.0.0-next.51

  sveltekit-superforms@2.27.1:
    resolution: {integrity: sha512-cvq2AevkZ0Zrk0w0gNM3kjcnJMtJ0jzu+2zqDoM9a+lZa+8bGpNl4YqxVkemiJNkGnFgNC8xr5xF5BlMzjookQ==}
    peerDependencies:
      '@sveltejs/kit': 1.x || 2.x
      svelte: 3.x || 4.x || >=5.0.0-next.51

  tabbable@6.2.0:
    resolution: {integrity: sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==}

  tailwind-merge@3.0.2:
    resolution: {integrity: sha512-l7z+OYZ7mu3DTqrL88RiKrKIqO3NcpEO8V/Od04bNpvk0kiIFndGEoqfuzvj4yuhRkHKjRkII2z+KS2HfPcSxw==}

  tailwind-merge@3.3.1:
    resolution: {integrity: sha512-gBXpgUm/3rp1lMZZrM/w7D8GKqshif0zAymAhbCyIt8KMe+0v9DQ7cdYLR4FHH/cKpdTXb+A/tKKU3eolfsI+g==}

  tailwind-variants@1.0.0:
    resolution: {integrity: sha512-2WSbv4ulEEyuBKomOunut65D8UZwxrHoRfYnxGcQNnHqlSCp2+B7Yz2W+yrNDrxRodOXtGD/1oCcKGNBnUqMqA==}
    engines: {node: '>=16.x', pnpm: '>=7.x'}
    peerDependencies:
      tailwindcss: '*'

  tailwindcss@4.1.11:
    resolution: {integrity: sha512-2E9TBm6MDD/xKYe+dvJZAmg3yxIEDNRc0jwlNyDg/4Fil2QcSLjFKGVff0lAf1jjeaArlG/M75Ey/EYr/OJtBA==}

  tapable@2.2.2:
    resolution: {integrity: sha512-Re10+NauLTMCudc7T5WLFLAwDhQ0JWdrMK+9B2M8zR5hRExKmsRDCBA7/aV/pNJFltmBFO5BAMlQFi/vq3nKOg==}
    engines: {node: '>=6'}

  tar@7.4.3:
    resolution: {integrity: sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw==}
    engines: {node: '>=18'}

  test-exclude@7.0.1:
    resolution: {integrity: sha512-pFYqmTw68LXVjeWJMST4+borgQP2AyMNbg1BpZh9LbyhUeNkeaPF9gzfPGUAnSMV3qPYdWUwDIjjCLiSDOl7vg==}
    engines: {node: '>=18'}

  tiny-case@1.0.3:
    resolution: {integrity: sha512-Eet/eeMhkO6TX8mnUteS9zgPbUMQa4I6Kkp5ORiBD5476/m+PIRiumP5tmh5ioJpH7k51Kehawy2UDfsnxxY8Q==}

  tiny-glob@0.2.9:
    resolution: {integrity: sha512-g/55ssRPUjShh+xkfx9UPDXqhckHEsHr4Vd9zX55oSdGZc/MD0m3sferOkwWtp98bv+kcVfEHtRJgBVJzelrzg==}

  tiny-invariant@1.3.3:
    resolution: {integrity: sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==}

  tinybench@2.9.0:
    resolution: {integrity: sha512-0+DUvqWMValLmha6lr4kD8iAMK1HzV0/aKnCtWb9v9641TnP/MFb7Pc2bxoxQjTXAErryXVgUOfv2YqNllqGeg==}

  tinyexec@0.3.2:
    resolution: {integrity: sha512-KQQR9yN7R5+OSwaK0XQoj22pwHoTlgYqmUscPYoknOoWCWfj/5/ABTMRi69FrKU5ffPVh5QcFikpWJI/P1ocHA==}

  tinyglobby@0.2.14:
    resolution: {integrity: sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==}
    engines: {node: '>=12.0.0'}

  tinypool@1.1.1:
    resolution: {integrity: sha512-Zba82s87IFq9A9XmjiX5uZA/ARWDrB03OHlq+Vw1fSdt0I+4/Kutwy8BP4Y/y/aORMo61FQ0vIb5j44vSo5Pkg==}
    engines: {node: ^18.0.0 || >=20.0.0}

  tinyrainbow@2.0.0:
    resolution: {integrity: sha512-op4nsTR47R6p0vMUUoYl/a+ljLFVtlfaXkLQmqfLR1qHma1h/ysYk4hEXZ880bf2CYgTskvTa/e196Vd5dDQXw==}
    engines: {node: '>=14.0.0'}

  tinyspy@3.0.2:
    resolution: {integrity: sha512-n1cw8k1k0x4pgA2+9XrOkFydTerNcJ1zWCO5Nn9scWHTD+5tp8dghT2x1uduQePZTZgd3Tupf+x9BxJjeJi77Q==}
    engines: {node: '>=14.0.0'}

  tinyspy@4.0.3:
    resolution: {integrity: sha512-t2T/WLB2WRgZ9EpE4jgPJ9w+i66UZfDc8wHh0xrwiRNN+UwH98GIJkTeZqX9rg0i0ptwzqW+uYeIF0T4F8LR7A==}
    engines: {node: '>=14.0.0'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  toposort@2.0.2:
    resolution: {integrity: sha512-0a5EOkAUp8D4moMi2W8ZF8jcga7BgZd91O/yabJCFY8az+XSzeGyTKs0Aoo897iV1Nj6guFq8orWDS96z91oGg==}

  totalist@3.0.1:
    resolution: {integrity: sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==}
    engines: {node: '>=6'}

  tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}

  ts-algebra@2.0.0:
    resolution: {integrity: sha512-FPAhNPFMrkwz76P7cdjdmiShwMynZYN6SgOujD1urY4oNm80Ou9oMdmbR45LotcKOXoy7wSmHkRFE6Mxbrhefw==}

  ts-api-utils@2.1.0:
    resolution: {integrity: sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==}
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'

  ts-dedent@2.2.0:
    resolution: {integrity: sha512-q5W7tVM71e2xjHZTlgfTDoPF/SmqKG5hddq9SzR49CH2hayqRKJtQ4mtRlSxKaJlR/+9rEM+mnBHf7I2/BQcpQ==}
    engines: {node: '>=6.10'}

  ts-deepmerge@7.0.3:
    resolution: {integrity: sha512-Du/ZW2RfwV/D4cmA5rXafYjBQVuvu4qGiEEla4EmEHVHgRdx68Gftx7i66jn2bzHPwSVZY36Ae6OuDn9el4ZKA==}
    engines: {node: '>=14.13.1'}

  tslib@2.4.0:
    resolution: {integrity: sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  tw-animate-css@1.3.5:
    resolution: {integrity: sha512-t3u+0YNoloIhj1mMXs779P6MO9q3p3mvGn4k1n3nJPqJw/glZcuijG2qTSN4z4mgNRfW5ZC3aXJFLwDtiipZXA==}

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  type-fest@2.19.0:
    resolution: {integrity: sha512-RAH822pAdBgcNMAfWnCBU3CFZcfZ/i1eZjwFU/dsLKumyuuP3niueg2UAukXYF0E2AAoc82ZSSf9J0WQBinzHA==}
    engines: {node: '>=12.20'}

  typescript-eslint@8.36.0:
    resolution: {integrity: sha512-fTCqxthY+h9QbEgSIBfL9iV6CvKDFuoxg6bHPNpJ9HIUzS+jy2lCEyCmGyZRWEBSaykqcDPf1SJ+BfCI8DRopA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  uid@2.0.2:
    resolution: {integrity: sha512-u3xV3X7uzvi5b1MncmZo3i2Aw222Zk1keqLA1YkHldREkAhAqi65wuPfe7lHx8H/Wzy+8CE7S7uS3jekIM5s8g==}
    engines: {node: '>=8'}

  undici-types@7.8.0:
    resolution: {integrity: sha512-9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw==}

  universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}

  unplugin@1.0.1:
    resolution: {integrity: sha512-aqrHaVBWW1JVKBHmGo33T5TxeL0qWzfvjWokObHA9bYmN7eNDkwOxmLjhioHl9878qDFMAaT51XNroRyuz7WxA==}

  unplugin@1.16.1:
    resolution: {integrity: sha512-4/u/j4FrCKdi17jaxuJA0jClGxB1AvU2hw/IuayPc4ay1XGaJs/rbb4v5WKwAjNifjmXK9PIFyuPiaK8azyR9w==}
    engines: {node: '>=14.0.0'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  valibot@0.42.1:
    resolution: {integrity: sha512-3keXV29Ar5b//Hqi4MbSdV7lfVp6zuYLZuA9V1PvQUsXqogr+u5lvLPLk3A4f74VUXDnf/JfWMN6sB+koJ/FFw==}
    peerDependencies:
      typescript: '>=5'
    peerDependenciesMeta:
      typescript:
        optional: true

  valibot@1.1.0:
    resolution: {integrity: sha512-Nk8lX30Qhu+9txPYTwM0cFlWLdPFsFr6LblzqIySfbZph9+BFsAHsNvHOymEviUepeIW6KFHzpX8TKhbptBXXw==}
    peerDependencies:
      typescript: '>=5'
    peerDependenciesMeta:
      typescript:
        optional: true

  validator@13.15.15:
    resolution: {integrity: sha512-BgWVbCI72aIQy937xbawcs+hrVaN/CZ2UwutgaJ36hGqRrLNM+f5LUT/YPRbo8IV/ASeFzXszezV+y2+rq3l8A==}
    engines: {node: '>= 0.10'}

  vite-node@3.2.4:
    resolution: {integrity: sha512-EbKSKh+bh1E1IFxeO0pg1n4dvoOTt0UDiXMd/qn++r98+jPO1xtJilvXldeuQ8giIB5IkpjCgMleHMNEsGH6pg==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true

  vite@6.3.5:
    resolution: {integrity: sha512-cZn6NDFE7wdTpINgs++ZJ4N49W2vRp8LCKrn3Ob1kYNtOo21vfDoaV5GzBfLU4MovSAB8uNRm4jgzVQZ+mBzPQ==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: '>=1.21.0'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  vite@7.0.3:
    resolution: {integrity: sha512-y2L5oJZF7bj4c0jgGYgBNSdIu+5HF+m68rn2cQXFbGoShdhV1phX9rbnxy9YXj82aS8MMsCLAAFkRxZeWdldrQ==}
    engines: {node: ^20.19.0 || >=22.12.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^20.19.0 || >=22.12.0
      jiti: '>=1.21.0'
      less: ^4.0.0
      lightningcss: ^1.21.0
      sass: ^1.70.0
      sass-embedded: ^1.70.0
      stylus: '>=0.54.8'
      sugarss: ^5.0.0
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  vitefu@1.1.1:
    resolution: {integrity: sha512-B/Fegf3i8zh0yFbpzZ21amWzHmuNlLlmJT6n7bu5e+pCHUKQIfXSYokrqOBGEMMe9UG2sostKQF9mml/vYaWJQ==}
    peerDependencies:
      vite: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0
    peerDependenciesMeta:
      vite:
        optional: true

  vitest-browser-svelte@1.0.0:
    resolution: {integrity: sha512-rjK4uMM6D6IgvJqvLNRo7vBbUfDcnoHHlGdjN/1zZG+u8qpe0cl9v85nOKcqgW3sNzVF5Hti7SrwlH20BvsvOg==}
    engines: {node: ^18.0.0 || >=20.0.0}
    peerDependencies:
      '@vitest/browser': ^2.1.0 || ^3.0.0 || ^4.0.0-0
      svelte: ^3 || ^4 || ^5 || ^5.0.0-next.0
      vitest: ^2.1.0 || ^3.0.0 || ^4.0.0-0

  vitest@3.2.4:
    resolution: {integrity: sha512-LUCP5ev3GURDysTWiP47wRRUpLKMOfPh+yKTx3kVIEiu5KOMeqzpnYNsKyOoVrULivR8tLcks4+lga33Whn90A==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@edge-runtime/vm': '*'
      '@types/debug': ^4.1.12
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      '@vitest/browser': 3.2.4
      '@vitest/ui': 3.2.4
      happy-dom: '*'
      jsdom: '*'
    peerDependenciesMeta:
      '@edge-runtime/vm':
        optional: true
      '@types/debug':
        optional: true
      '@types/node':
        optional: true
      '@vitest/browser':
        optional: true
      '@vitest/ui':
        optional: true
      happy-dom:
        optional: true
      jsdom:
        optional: true

  web-streams-polyfill@3.3.3:
    resolution: {integrity: sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==}
    engines: {node: '>= 8'}

  webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}

  webpack-sources@3.3.3:
    resolution: {integrity: sha512-yd1RBzSGanHkitROoPFd6qsrxt+oFhg/129YzheDGqeustzX0vTZJZsSsQjVQC4yzBQ56K55XU8gaNCtIzOnTg==}
    engines: {node: '>=10.13.0'}

  webpack-virtual-modules@0.5.0:
    resolution: {integrity: sha512-kyDivFZ7ZM0BVOUteVbDFhlRt7Ah/CSPwJdi8hBpkK7QLumUqdLtVfm/PX/hkcnrvr0i77fO5+TjZ94Pe+C9iw==}

  webpack-virtual-modules@0.6.2:
    resolution: {integrity: sha512-66/V2i5hQanC51vBQKPH4aI8NMAcBW59FVBs+rC7eGHupMyfn34q7rZIE+ETlJ+XTevqfUhVVBgSUNSW2flEUQ==}

  whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  why-is-node-running@2.3.0:
    resolution: {integrity: sha512-hUrmaWBdVDcxvYqnyh09zunKzROWjbZTiNy8dBEjkS7ehEDQibXJ7XvlmtbwuTclUiIyN+CyXQD4Vmko8fNm8w==}
    engines: {node: '>=8'}
    hasBin: true

  word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}

  write-file-atomic@6.0.0:
    resolution: {integrity: sha512-GmqrO8WJ1NuzJ2DrziEI2o57jKAVIQNf8a18W3nCYU3H7PNWqCCVTeH6/NQE93CIllIgQS98rrmVkYgTX9fFJQ==}
    engines: {node: ^18.17.0 || >=20.5.0}

  ws@8.18.3:
    resolution: {integrity: sha512-PEIGCY5tSlUt50cqyMXfCzX+oOPqN0vuGqWzbcJ2xvnkzkq46oOpz7dQaTDBdfICb4N14+GARUDw2XV2N4tvzg==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xlsx@file:vendor/xlsx-0.20.3.tgz:
    resolution: {integrity: sha512-oLDq3jw7AcLqKWH2AhCpVTZl8mf6X2YReP+Neh0SJUzV/BdZYjth94tG5toiMB1PPrYtxOCfaoUCkvtuH+3AJA==, tarball: file:vendor/xlsx-0.20.3.tgz}
    version: 0.20.3
    engines: {node: '>=0.8'}
    hasBin: true

  xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yallist@5.0.0:
    resolution: {integrity: sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw==}
    engines: {node: '>=18'}

  yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

  yup@1.6.1:
    resolution: {integrity: sha512-JED8pB50qbA4FOkDol0bYF/p60qSEDQqBD0/qeIrUCG1KbPBIQ776fCUNb9ldbPcSTxA69g/47XTo4TqWiuXOA==}

  zimmerframe@1.1.2:
    resolution: {integrity: sha512-rAbqEGa8ovJy4pyBxZM70hg4pE6gDgaQ0Sl9M3enG3I0d6H4XSAM3GeNGLKnsBpuijUow064sf7ww1nutC5/3w==}

  zod-to-json-schema@3.24.6:
    resolution: {integrity: sha512-h/z3PKvcTcTetyjl1fkj79MHNEjm+HpD6NXheWjzOekY7kV+lwDYnHw+ivHkijnCSMz1yJaWBD9vu/Fcmk+vEg==}
    peerDependencies:
      zod: ^3.24.1

  zod@3.25.76:
    resolution: {integrity: sha512-gzUt/qt81nXsFGKIFcC3YnfEAx5NkunCfnDlvuBSSFS02bcXu4Lmea0AFIUwbLWxWPx3d9p8S5QoaujKcNQxcQ==}

snapshots:

  '@adobe/css-tools@4.4.3': {}

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29

  '@ark/schema@0.46.0':
    dependencies:
      '@ark/util': 0.46.0
    optional: true

  '@ark/util@0.46.0':
    optional: true

  '@babel/code-frame@7.27.1':
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.28.0': {}

  '@babel/core@7.28.0':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.28.0)
      '@babel/helpers': 7.27.6
      '@babel/parser': 7.28.0
      '@babel/template': 7.27.2
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.0
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.28.0':
    dependencies:
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.0
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29
      jsesc: 3.1.0

  '@babel/helper-compilation-targets@7.27.2':
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.1
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-globals@7.28.0': {}

  '@babel/helper-module-imports@7.27.1':
    dependencies:
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/helper-validator-option@7.27.1': {}

  '@babel/helpers@7.27.6':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.28.0

  '@babel/parser@7.26.9':
    dependencies:
      '@babel/types': 7.28.0

  '@babel/parser@7.28.0':
    dependencies:
      '@babel/types': 7.28.0

  '@babel/runtime@7.27.6': {}

  '@babel/template@7.27.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.0

  '@babel/traverse@7.28.0':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.0
      '@babel/helper-globals': 7.28.0
      '@babel/parser': 7.28.0
      '@babel/template': 7.27.2
      '@babel/types': 7.28.0
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.28.0':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@bcoe/v8-coverage@1.0.2': {}

  '@chromatic-com/storybook@4.0.1(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))':
    dependencies:
      '@neoconfetti/react': 1.0.0
      chromatic: 12.2.0
      filesize: 10.1.6
      jsonfile: 6.1.0
      storybook: 9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2)
      strip-ansi: 7.1.0
    transitivePeerDependencies:
      - '@chromatic-com/cypress'
      - '@chromatic-com/playwright'

  '@dagrejs/dagre@1.1.5':
    dependencies:
      '@dagrejs/graphlib': 2.2.4

  '@dagrejs/graphlib@2.2.4': {}

  '@esbuild/aix-ppc64@0.25.6':
    optional: true

  '@esbuild/android-arm64@0.25.6':
    optional: true

  '@esbuild/android-arm@0.25.6':
    optional: true

  '@esbuild/android-x64@0.25.6':
    optional: true

  '@esbuild/darwin-arm64@0.25.6':
    optional: true

  '@esbuild/darwin-x64@0.25.6':
    optional: true

  '@esbuild/freebsd-arm64@0.25.6':
    optional: true

  '@esbuild/freebsd-x64@0.25.6':
    optional: true

  '@esbuild/linux-arm64@0.25.6':
    optional: true

  '@esbuild/linux-arm@0.25.6':
    optional: true

  '@esbuild/linux-ia32@0.25.6':
    optional: true

  '@esbuild/linux-loong64@0.25.6':
    optional: true

  '@esbuild/linux-mips64el@0.25.6':
    optional: true

  '@esbuild/linux-ppc64@0.25.6':
    optional: true

  '@esbuild/linux-riscv64@0.25.6':
    optional: true

  '@esbuild/linux-s390x@0.25.6':
    optional: true

  '@esbuild/linux-x64@0.25.6':
    optional: true

  '@esbuild/netbsd-arm64@0.25.6':
    optional: true

  '@esbuild/netbsd-x64@0.25.6':
    optional: true

  '@esbuild/openbsd-arm64@0.25.6':
    optional: true

  '@esbuild/openbsd-x64@0.25.6':
    optional: true

  '@esbuild/openharmony-arm64@0.25.6':
    optional: true

  '@esbuild/sunos-x64@0.25.6':
    optional: true

  '@esbuild/win32-arm64@0.25.6':
    optional: true

  '@esbuild/win32-ia32@0.25.6':
    optional: true

  '@esbuild/win32-x64@0.25.6':
    optional: true

  '@eslint-community/eslint-utils@4.7.0(eslint@9.30.1(jiti@2.4.2))':
    dependencies:
      eslint: 9.30.1(jiti@2.4.2)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/compat@1.3.1(eslint@9.30.1(jiti@2.4.2))':
    optionalDependencies:
      eslint: 9.30.1(jiti@2.4.2)

  '@eslint/config-array@0.21.0':
    dependencies:
      '@eslint/object-schema': 2.1.6
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/config-helpers@0.3.0': {}

  '@eslint/core@0.14.0':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/core@0.15.1':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@3.3.1':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 10.4.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.30.1': {}

  '@eslint/object-schema@2.1.6': {}

  '@eslint/plugin-kit@0.3.3':
    dependencies:
      '@eslint/core': 0.15.1
      levn: 0.4.1

  '@exodus/schemasafe@1.3.0':
    optional: true

  '@floating-ui/core@1.7.2':
    dependencies:
      '@floating-ui/utils': 0.2.10

  '@floating-ui/dom@1.7.2':
    dependencies:
      '@floating-ui/core': 1.7.2
      '@floating-ui/utils': 0.2.10

  '@floating-ui/utils@0.2.10': {}

  '@gcornut/valibot-json-schema@0.42.0(esbuild@0.25.6)(typescript@5.8.3)':
    dependencies:
      valibot: 0.42.1(typescript@5.8.3)
    optionalDependencies:
      '@types/json-schema': 7.0.15
      esbuild-runner: 2.2.2(esbuild@0.25.6)
    transitivePeerDependencies:
      - esbuild
      - typescript
    optional: true

  '@hapi/hoek@9.3.0':
    optional: true

  '@hapi/topo@5.1.0':
    dependencies:
      '@hapi/hoek': 9.3.0
    optional: true

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.6':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.3.1': {}

  '@humanwhocodes/retry@0.4.3': {}

  '@internationalized/date@3.8.2':
    dependencies:
      '@swc/helpers': 0.5.17

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@isaacs/fs-minipass@4.0.1':
    dependencies:
      minipass: 7.1.2

  '@istanbuljs/schema@0.1.3': {}

  '@jridgewell/gen-mapping@0.3.12':
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4
      '@jridgewell/trace-mapping': 0.3.29

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/sourcemap-codec@1.5.4': {}

  '@jridgewell/trace-mapping@0.3.29':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.4

  '@layerstack/svelte-actions@1.0.1-next.2':
    dependencies:
      '@floating-ui/dom': 1.7.2
      '@layerstack/utils': 1.1.0-next.2
      d3-array: 3.2.4
      d3-scale: 4.0.2
      date-fns: 4.1.0
      lodash-es: 4.17.21

  '@layerstack/svelte-state@0.1.0-next.3':
    dependencies:
      '@layerstack/utils': 1.1.0-next.2

  '@layerstack/svelte-stores@1.0.2-next.2':
    dependencies:
      '@layerstack/utils': 1.1.0-next.2
      d3-array: 3.2.4
      date-fns: 4.1.0
      immer: 10.1.1
      lodash-es: 4.17.21
      zod: 3.25.76

  '@layerstack/tailwind@2.0.0-next.4':
    dependencies:
      '@layerstack/utils': 1.1.0-next.2
      clsx: 2.1.1
      culori: 4.0.2
      d3-array: 3.2.4
      date-fns: 4.1.0
      lodash-es: 4.17.21
      tailwind-merge: 3.3.1
      tailwindcss: 4.1.11

  '@layerstack/utils@1.1.0-next.2':
    dependencies:
      d3-array: 3.2.4
      date-fns: 4.1.0
      lodash-es: 4.17.21

  '@lucide/svelte@0.525.0(svelte@5.35.4)':
    dependencies:
      svelte: 5.35.4

  '@lukeed/csprng@1.1.0': {}

  '@mapbox/node-pre-gyp@2.0.0':
    dependencies:
      consola: 3.4.2
      detect-libc: 2.0.4
      https-proxy-agent: 7.0.6
      node-fetch: 2.7.0
      nopt: 8.1.0
      semver: 7.7.2
      tar: 7.4.3
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@mdx-js/react@3.1.0(@types/react@19.1.8)(react@19.1.0)':
    dependencies:
      '@types/mdx': 2.0.13
      '@types/react': 19.1.8
      react: 19.1.0

  '@neoconfetti/react@1.0.0': {}

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@opentelemetry/api-logs@0.57.2':
    dependencies:
      '@opentelemetry/api': 1.9.0

  '@opentelemetry/api@1.9.0': {}

  '@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0

  '@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/semantic-conventions': 1.28.0

  '@opentelemetry/instrumentation-amqplib@0.46.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-connect@0.43.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
      '@types/connect': 3.4.38
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-dataloader@0.16.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-express@0.47.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-fs@0.19.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-generic-pool@0.43.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-graphql@0.47.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-hapi@0.45.2(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-http@0.57.2(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.28.0
      forwarded-parse: 2.1.2
      semver: 7.7.2
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-ioredis@0.47.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/redis-common': 0.36.2
      '@opentelemetry/semantic-conventions': 1.34.0
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-kafkajs@0.7.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-knex@0.44.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-koa@0.47.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-lru-memoizer@0.44.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-mongodb@0.52.0(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-mongoose@0.46.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-mysql2@0.45.2(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
      '@opentelemetry/sql-common': 0.40.1(@opentelemetry/api@1.9.0)
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-mysql@0.45.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
      '@types/mysql': 2.15.26
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-pg@0.51.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
      '@opentelemetry/sql-common': 0.40.1(@opentelemetry/api@1.9.0)
      '@types/pg': 8.6.1
      '@types/pg-pool': 2.0.6
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-redis-4@0.46.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/redis-common': 0.36.2
      '@opentelemetry/semantic-conventions': 1.34.0
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-tedious@0.18.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
      '@types/tedious': 4.0.14
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-undici@0.10.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/api-logs': 0.57.2
      '@types/shimmer': 1.2.0
      import-in-the-middle: 1.14.2
      require-in-the-middle: 7.5.2
      semver: 7.7.2
      shimmer: 1.2.1
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/redis-common@0.36.2': {}

  '@opentelemetry/resources@1.30.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.28.0

  '@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.28.0

  '@opentelemetry/semantic-conventions@1.28.0': {}

  '@opentelemetry/semantic-conventions@1.34.0': {}

  '@opentelemetry/sql-common@0.40.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@pkgr/core@0.2.7': {}

  '@playwright/test@1.53.2':
    dependencies:
      playwright: 1.53.2

  '@polka/url@1.0.0-next.29': {}

  '@poppinss/macroable@1.0.5':
    optional: true

  '@prisma/instrumentation@6.11.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
    transitivePeerDependencies:
      - supports-color

  '@react-email/render@1.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      html-to-text: 9.0.5
      prettier: 3.6.2
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-promise-suspense: 0.3.4

  '@rollup/pluginutils@5.2.0(rollup@4.44.2)':
    dependencies:
      '@types/estree': 1.0.8
      estree-walker: 2.0.2
      picomatch: 4.0.2
    optionalDependencies:
      rollup: 4.44.2

  '@rollup/rollup-android-arm-eabi@4.44.2':
    optional: true

  '@rollup/rollup-android-arm64@4.44.2':
    optional: true

  '@rollup/rollup-darwin-arm64@4.44.2':
    optional: true

  '@rollup/rollup-darwin-x64@4.44.2':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.44.2':
    optional: true

  '@rollup/rollup-freebsd-x64@4.44.2':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.44.2':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.44.2':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.44.2':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.44.2':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.44.2':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.44.2':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.44.2':
    optional: true

  '@rollup/rollup-linux-riscv64-musl@4.44.2':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.44.2':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.44.2':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.44.2':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.44.2':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.44.2':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.44.2':
    optional: true

  '@selderee/plugin-htmlparser2@0.11.0':
    dependencies:
      domhandler: 5.0.3
      selderee: 0.11.0

  '@sentry-internal/browser-utils@9.36.0':
    dependencies:
      '@sentry/core': 9.36.0

  '@sentry-internal/feedback@9.36.0':
    dependencies:
      '@sentry/core': 9.36.0

  '@sentry-internal/replay-canvas@9.36.0':
    dependencies:
      '@sentry-internal/replay': 9.36.0
      '@sentry/core': 9.36.0

  '@sentry-internal/replay@9.36.0':
    dependencies:
      '@sentry-internal/browser-utils': 9.36.0
      '@sentry/core': 9.36.0

  '@sentry/babel-plugin-component-annotate@3.5.0': {}

  '@sentry/browser@9.36.0':
    dependencies:
      '@sentry-internal/browser-utils': 9.36.0
      '@sentry-internal/feedback': 9.36.0
      '@sentry-internal/replay': 9.36.0
      '@sentry-internal/replay-canvas': 9.36.0
      '@sentry/core': 9.36.0

  '@sentry/bundler-plugin-core@3.5.0':
    dependencies:
      '@babel/core': 7.28.0
      '@sentry/babel-plugin-component-annotate': 3.5.0
      '@sentry/cli': 2.42.2
      dotenv: 16.6.1
      find-up: 5.0.0
      glob: 9.3.5
      magic-string: 0.30.8
      unplugin: 1.0.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@sentry/cli-darwin@2.42.2':
    optional: true

  '@sentry/cli-linux-arm64@2.42.2':
    optional: true

  '@sentry/cli-linux-arm@2.42.2':
    optional: true

  '@sentry/cli-linux-i686@2.42.2':
    optional: true

  '@sentry/cli-linux-x64@2.42.2':
    optional: true

  '@sentry/cli-win32-i686@2.42.2':
    optional: true

  '@sentry/cli-win32-x64@2.42.2':
    optional: true

  '@sentry/cli@2.42.2':
    dependencies:
      https-proxy-agent: 5.0.1
      node-fetch: 2.7.0
      progress: 2.0.3
      proxy-from-env: 1.1.0
      which: 2.0.2
    optionalDependencies:
      '@sentry/cli-darwin': 2.42.2
      '@sentry/cli-linux-arm': 2.42.2
      '@sentry/cli-linux-arm64': 2.42.2
      '@sentry/cli-linux-i686': 2.42.2
      '@sentry/cli-linux-x64': 2.42.2
      '@sentry/cli-win32-i686': 2.42.2
      '@sentry/cli-win32-x64': 2.42.2
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@sentry/cloudflare@9.36.0':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@sentry/core': 9.36.0

  '@sentry/core@9.36.0': {}

  '@sentry/node-core@9.36.0(@opentelemetry/api@1.9.0)(@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0))(@opentelemetry/resources@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/semantic-conventions@1.34.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/context-async-hooks': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-base': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
      '@sentry/core': 9.36.0
      '@sentry/opentelemetry': 9.36.0(@opentelemetry/api@1.9.0)(@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/semantic-conventions@1.34.0)
      import-in-the-middle: 1.14.2

  '@sentry/node@9.36.0':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/context-async-hooks': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-amqplib': 0.46.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-connect': 0.43.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-dataloader': 0.16.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-express': 0.47.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-fs': 0.19.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-generic-pool': 0.43.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-graphql': 0.47.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-hapi': 0.45.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-http': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-ioredis': 0.47.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-kafkajs': 0.7.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-knex': 0.44.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-koa': 0.47.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-lru-memoizer': 0.44.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-mongodb': 0.52.0(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-mongoose': 0.46.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-mysql': 0.45.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-mysql2': 0.45.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-pg': 0.51.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-redis-4': 0.46.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-tedious': 0.18.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-undici': 0.10.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-base': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
      '@prisma/instrumentation': 6.11.1(@opentelemetry/api@1.9.0)
      '@sentry/core': 9.36.0
      '@sentry/node-core': 9.36.0(@opentelemetry/api@1.9.0)(@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0))(@opentelemetry/resources@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/semantic-conventions@1.34.0)
      '@sentry/opentelemetry': 9.36.0(@opentelemetry/api@1.9.0)(@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/semantic-conventions@1.34.0)
      import-in-the-middle: 1.14.2
      minimatch: 9.0.5
    transitivePeerDependencies:
      - supports-color

  '@sentry/opentelemetry@9.36.0(@opentelemetry/api@1.9.0)(@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/semantic-conventions@1.34.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/context-async-hooks': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.57.2(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-base': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
      '@sentry/core': 9.36.0

  '@sentry/svelte@9.36.0(svelte@5.35.4)':
    dependencies:
      '@sentry/browser': 9.36.0
      '@sentry/core': 9.36.0
      magic-string: 0.30.7
      svelte: 5.35.4

  '@sentry/sveltekit@9.36.0(@sveltejs/kit@2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))':
    dependencies:
      '@babel/parser': 7.26.9
      '@sentry/cloudflare': 9.36.0
      '@sentry/core': 9.36.0
      '@sentry/node': 9.36.0
      '@sentry/svelte': 9.36.0(svelte@5.35.4)
      '@sentry/vite-plugin': 3.5.0
      '@sveltejs/kit': 2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      magic-string: 0.30.7
      recast: 0.23.11
      sorcery: 1.0.0
    optionalDependencies:
      vite: 6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)
    transitivePeerDependencies:
      - '@cloudflare/workers-types'
      - encoding
      - supports-color
      - svelte

  '@sentry/vite-plugin@3.5.0':
    dependencies:
      '@sentry/bundler-plugin-core': 3.5.0
      unplugin: 1.0.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@sideway/address@4.1.5':
    dependencies:
      '@hapi/hoek': 9.3.0
    optional: true

  '@sideway/formula@3.0.1':
    optional: true

  '@sideway/pinpoint@2.0.0':
    optional: true

  '@sinclair/typebox@0.34.37':
    optional: true

  '@standard-schema/spec@1.0.0':
    optional: true

  '@storybook/addon-docs@9.1.0-alpha.2(@types/react@19.1.8)(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))':
    dependencies:
      '@mdx-js/react': 3.1.0(@types/react@19.1.8)(react@19.1.0)
      '@storybook/csf-plugin': 9.1.0-alpha.2(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))
      '@storybook/icons': 1.4.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@storybook/react-dom-shim': 9.1.0-alpha.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      storybook: 9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2)
      ts-dedent: 2.2.0
    transitivePeerDependencies:
      - '@types/react'

  '@storybook/addon-svelte-csf@5.0.6(@storybook/svelte@9.1.0-alpha.2(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(svelte@5.35.4))(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))':
    dependencies:
      '@storybook/csf': 0.1.13
      '@storybook/svelte': 9.1.0-alpha.2(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(svelte@5.35.4)
      '@sveltejs/vite-plugin-svelte': 5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      dedent: 1.6.0
      es-toolkit: 1.39.7
      esrap: 1.4.9
      magic-string: 0.30.17
      storybook: 9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2)
      svelte: 5.35.4
      svelte-ast-print: 0.4.2(svelte@5.35.4)
      vite: 6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)
      zimmerframe: 1.1.2
    transitivePeerDependencies:
      - babel-plugin-macros

  '@storybook/addon-vitest@9.1.0-alpha.2(@vitest/browser@3.2.4)(@vitest/runner@3.2.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(vitest@3.2.4)':
    dependencies:
      '@storybook/global': 5.0.0
      '@storybook/icons': 1.4.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      prompts: 2.4.2
      storybook: 9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2)
      ts-dedent: 2.2.0
    optionalDependencies:
      '@vitest/browser': 3.2.4(playwright@1.53.2)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))(vitest@3.2.4)
      '@vitest/runner': 3.2.4
      vitest: 3.2.4(@types/node@24.0.12)(@vitest/browser@3.2.4)(jiti@2.4.2)(lightningcss@1.30.1)
    transitivePeerDependencies:
      - react
      - react-dom

  '@storybook/builder-vite@9.1.0-alpha.2(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))':
    dependencies:
      '@storybook/csf-plugin': 9.1.0-alpha.2(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))
      storybook: 9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2)
      ts-dedent: 2.2.0
      vite: 6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)

  '@storybook/csf-plugin@9.1.0-alpha.2(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))':
    dependencies:
      storybook: 9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2)
      unplugin: 1.16.1

  '@storybook/csf@0.1.13':
    dependencies:
      type-fest: 2.19.0

  '@storybook/global@5.0.0': {}

  '@storybook/icons@1.4.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@storybook/react-dom-shim@9.1.0-alpha.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))':
    dependencies:
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      storybook: 9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2)

  '@storybook/svelte-vite@9.1.0-alpha.2(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))':
    dependencies:
      '@storybook/builder-vite': 9.1.0-alpha.2(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      '@storybook/svelte': 9.1.0-alpha.2(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(svelte@5.35.4)
      '@sveltejs/vite-plugin-svelte': 5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      magic-string: 0.30.17
      storybook: 9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2)
      svelte: 5.35.4
      svelte2tsx: 0.7.40(svelte@5.35.4)(typescript@5.8.3)
      typescript: 5.8.3
      vite: 6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)

  '@storybook/svelte@9.1.0-alpha.2(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(svelte@5.35.4)':
    dependencies:
      storybook: 9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2)
      svelte: 5.35.4
      ts-dedent: 2.2.0
      type-fest: 2.19.0

  '@storybook/sveltekit@9.1.0-alpha.2(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))':
    dependencies:
      '@storybook/builder-vite': 9.1.0-alpha.2(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      '@storybook/svelte': 9.1.0-alpha.2(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(svelte@5.35.4)
      '@storybook/svelte-vite': 9.1.0-alpha.2(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      storybook: 9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2)
      svelte: 5.35.4
      vite: 6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)
    transitivePeerDependencies:
      - '@sveltejs/vite-plugin-svelte'

  '@supabase/auth-js@2.70.0':
    dependencies:
      '@supabase/node-fetch': 2.6.15

  '@supabase/functions-js@2.4.5':
    dependencies:
      '@supabase/node-fetch': 2.6.15

  '@supabase/node-fetch@2.6.15':
    dependencies:
      whatwg-url: 5.0.0

  '@supabase/postgrest-js@1.19.4':
    dependencies:
      '@supabase/node-fetch': 2.6.15

  '@supabase/realtime-js@2.11.15':
    dependencies:
      '@supabase/node-fetch': 2.6.15
      '@types/phoenix': 1.6.6
      '@types/ws': 8.18.1
      isows: 1.0.7(ws@8.18.3)
      ws: 8.18.3
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  '@supabase/ssr@0.6.1(@supabase/supabase-js@2.50.3)':
    dependencies:
      '@supabase/supabase-js': 2.50.3
      cookie: 1.0.2

  '@supabase/storage-js@2.7.1':
    dependencies:
      '@supabase/node-fetch': 2.6.15

  '@supabase/supabase-js@2.50.3':
    dependencies:
      '@supabase/auth-js': 2.70.0
      '@supabase/functions-js': 2.4.5
      '@supabase/node-fetch': 2.6.15
      '@supabase/postgrest-js': 1.19.4
      '@supabase/realtime-js': 2.11.15
      '@supabase/storage-js': 2.7.1
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  '@sveltejs/acorn-typescript@1.0.5(acorn@8.15.0)':
    dependencies:
      acorn: 8.15.0

  '@sveltejs/adapter-vercel@5.7.2(@sveltejs/kit@2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(rollup@4.44.2)':
    dependencies:
      '@sveltejs/kit': 2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      '@vercel/nft': 0.29.4(rollup@4.44.2)
      esbuild: 0.25.6
    transitivePeerDependencies:
      - encoding
      - rollup
      - supports-color

  '@sveltejs/kit@2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))':
    dependencies:
      '@sveltejs/acorn-typescript': 1.0.5(acorn@8.15.0)
      '@sveltejs/vite-plugin-svelte': 5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      '@types/cookie': 0.6.0
      acorn: 8.15.0
      cookie: 0.6.0
      devalue: 5.1.1
      esm-env: 1.2.2
      kleur: 4.1.5
      magic-string: 0.30.17
      mrmime: 2.0.1
      sade: 1.8.1
      set-cookie-parser: 2.7.1
      sirv: 3.0.1
      svelte: 5.35.4
      vite: 6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)

  '@sveltejs/vite-plugin-svelte-inspector@4.0.1(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))':
    dependencies:
      '@sveltejs/vite-plugin-svelte': 5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      debug: 4.4.1
      svelte: 5.35.4
      vite: 6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)
    transitivePeerDependencies:
      - supports-color

  '@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))':
    dependencies:
      '@sveltejs/vite-plugin-svelte-inspector': 4.0.1(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      debug: 4.4.1
      deepmerge: 4.3.1
      kleur: 4.1.5
      magic-string: 0.30.17
      svelte: 5.35.4
      vite: 6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)
      vitefu: 1.1.1(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
    transitivePeerDependencies:
      - supports-color

  '@swc/helpers@0.5.17':
    dependencies:
      tslib: 2.8.1

  '@tailwindcss/container-queries@0.1.1(tailwindcss@4.1.11)':
    dependencies:
      tailwindcss: 4.1.11

  '@tailwindcss/forms@0.5.10(tailwindcss@4.1.11)':
    dependencies:
      mini-svg-data-uri: 1.4.4
      tailwindcss: 4.1.11

  '@tailwindcss/node@4.1.11':
    dependencies:
      '@ampproject/remapping': 2.3.0
      enhanced-resolve: 5.18.2
      jiti: 2.4.2
      lightningcss: 1.30.1
      magic-string: 0.30.17
      source-map-js: 1.2.1
      tailwindcss: 4.1.11

  '@tailwindcss/oxide-android-arm64@4.1.11':
    optional: true

  '@tailwindcss/oxide-darwin-arm64@4.1.11':
    optional: true

  '@tailwindcss/oxide-darwin-x64@4.1.11':
    optional: true

  '@tailwindcss/oxide-freebsd-x64@4.1.11':
    optional: true

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11':
    optional: true

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.11':
    optional: true

  '@tailwindcss/oxide-linux-arm64-musl@4.1.11':
    optional: true

  '@tailwindcss/oxide-linux-x64-gnu@4.1.11':
    optional: true

  '@tailwindcss/oxide-linux-x64-musl@4.1.11':
    optional: true

  '@tailwindcss/oxide-wasm32-wasi@4.1.11':
    optional: true

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.11':
    optional: true

  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    optional: true

  '@tailwindcss/oxide@4.1.11':
    dependencies:
      detect-libc: 2.0.4
      tar: 7.4.3
    optionalDependencies:
      '@tailwindcss/oxide-android-arm64': 4.1.11
      '@tailwindcss/oxide-darwin-arm64': 4.1.11
      '@tailwindcss/oxide-darwin-x64': 4.1.11
      '@tailwindcss/oxide-freebsd-x64': 4.1.11
      '@tailwindcss/oxide-linux-arm-gnueabihf': 4.1.11
      '@tailwindcss/oxide-linux-arm64-gnu': 4.1.11
      '@tailwindcss/oxide-linux-arm64-musl': 4.1.11
      '@tailwindcss/oxide-linux-x64-gnu': 4.1.11
      '@tailwindcss/oxide-linux-x64-musl': 4.1.11
      '@tailwindcss/oxide-wasm32-wasi': 4.1.11
      '@tailwindcss/oxide-win32-arm64-msvc': 4.1.11
      '@tailwindcss/oxide-win32-x64-msvc': 4.1.11

  '@tailwindcss/typography@0.5.16(tailwindcss@4.1.11)':
    dependencies:
      lodash.castarray: 4.4.0
      lodash.isplainobject: 4.0.6
      lodash.merge: 4.6.2
      postcss-selector-parser: 6.0.10
      tailwindcss: 4.1.11

  '@tailwindcss/vite@4.1.11(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))':
    dependencies:
      '@tailwindcss/node': 4.1.11
      '@tailwindcss/oxide': 4.1.11
      tailwindcss: 4.1.11
      vite: 6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)

  '@tanstack/table-core@8.21.3': {}

  '@testing-library/dom@10.4.0':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/runtime': 7.27.6
      '@types/aria-query': 5.0.4
      aria-query: 5.3.0
      chalk: 4.1.2
      dom-accessibility-api: 0.5.16
      lz-string: 1.5.0
      pretty-format: 27.5.1

  '@testing-library/jest-dom@6.6.3':
    dependencies:
      '@adobe/css-tools': 4.4.3
      aria-query: 5.3.2
      chalk: 3.0.0
      css.escape: 1.5.1
      dom-accessibility-api: 0.6.3
      lodash: 4.17.21
      redent: 3.0.0

  '@testing-library/user-event@14.6.1(@testing-library/dom@10.4.0)':
    dependencies:
      '@testing-library/dom': 10.4.0

  '@types/aria-query@5.0.4': {}

  '@types/chai@5.2.2':
    dependencies:
      '@types/deep-eql': 4.0.2

  '@types/connect@3.4.38':
    dependencies:
      '@types/node': 24.0.12

  '@types/cookie@0.6.0': {}

  '@types/d3-color@3.1.3': {}

  '@types/d3-hierarchy@3.1.7': {}

  '@types/d3-scale-chromatic@3.1.0': {}

  '@types/d3-scale@4.0.9':
    dependencies:
      '@types/d3-time': 3.0.4

  '@types/d3-time@3.0.4': {}

  '@types/deep-eql@4.0.2': {}

  '@types/estree@1.0.8': {}

  '@types/json-schema@7.0.15': {}

  '@types/mdx@2.0.13': {}

  '@types/mysql@2.15.26':
    dependencies:
      '@types/node': 24.0.12

  '@types/node@24.0.12':
    dependencies:
      undici-types: 7.8.0

  '@types/pegjs@0.10.6': {}

  '@types/pg-pool@2.0.6':
    dependencies:
      '@types/pg': 8.6.1

  '@types/pg@8.6.1':
    dependencies:
      '@types/node': 24.0.12
      pg-protocol: 1.10.3
      pg-types: 2.2.0

  '@types/phoenix@1.6.6': {}

  '@types/react@19.1.8':
    dependencies:
      csstype: 3.1.3

  '@types/shimmer@1.2.0': {}

  '@types/tedious@4.0.14':
    dependencies:
      '@types/node': 24.0.12

  '@types/validator@13.15.2':
    optional: true

  '@types/ws@8.18.1':
    dependencies:
      '@types/node': 24.0.12

  '@typeschema/class-validator@0.3.0(@types/json-schema@7.0.15)(class-validator@0.14.2)':
    dependencies:
      '@typeschema/core': 0.14.0(@types/json-schema@7.0.15)
    optionalDependencies:
      class-validator: 0.14.2
    transitivePeerDependencies:
      - '@types/json-schema'
    optional: true

  '@typeschema/core@0.14.0(@types/json-schema@7.0.15)':
    optionalDependencies:
      '@types/json-schema': 7.0.15
    optional: true

  '@typescript-eslint/eslint-plugin@8.36.0(@typescript-eslint/parser@8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3))(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/scope-manager': 8.36.0
      '@typescript-eslint/type-utils': 8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/utils': 8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.36.0
      eslint: 9.30.1(jiti@2.4.2)
      graphemer: 1.4.0
      ignore: 7.0.5
      natural-compare: 1.4.0
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.36.0
      '@typescript-eslint/types': 8.36.0
      '@typescript-eslint/typescript-estree': 8.36.0(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.36.0
      debug: 4.4.1
      eslint: 9.30.1(jiti@2.4.2)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/project-service@8.36.0(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/tsconfig-utils': 8.36.0(typescript@5.8.3)
      '@typescript-eslint/types': 8.36.0
      debug: 4.4.1
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@8.36.0':
    dependencies:
      '@typescript-eslint/types': 8.36.0
      '@typescript-eslint/visitor-keys': 8.36.0

  '@typescript-eslint/tsconfig-utils@8.36.0(typescript@5.8.3)':
    dependencies:
      typescript: 5.8.3

  '@typescript-eslint/type-utils@8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/typescript-estree': 8.36.0(typescript@5.8.3)
      '@typescript-eslint/utils': 8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3)
      debug: 4.4.1
      eslint: 9.30.1(jiti@2.4.2)
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@8.36.0': {}

  '@typescript-eslint/typescript-estree@8.36.0(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/project-service': 8.36.0(typescript@5.8.3)
      '@typescript-eslint/tsconfig-utils': 8.36.0(typescript@5.8.3)
      '@typescript-eslint/types': 8.36.0
      '@typescript-eslint/visitor-keys': 8.36.0
      debug: 4.4.1
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.2
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.30.1(jiti@2.4.2))
      '@typescript-eslint/scope-manager': 8.36.0
      '@typescript-eslint/types': 8.36.0
      '@typescript-eslint/typescript-estree': 8.36.0(typescript@5.8.3)
      eslint: 9.30.1(jiti@2.4.2)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@8.36.0':
    dependencies:
      '@typescript-eslint/types': 8.36.0
      eslint-visitor-keys: 4.2.1

  '@vercel/nft@0.29.4(rollup@4.44.2)':
    dependencies:
      '@mapbox/node-pre-gyp': 2.0.0
      '@rollup/pluginutils': 5.2.0(rollup@4.44.2)
      acorn: 8.15.0
      acorn-import-attributes: 1.9.5(acorn@8.15.0)
      async-sema: 3.1.1
      bindings: 1.5.0
      estree-walker: 2.0.2
      glob: 10.4.5
      graceful-fs: 4.2.11
      node-gyp-build: 4.8.4
      picomatch: 4.0.2
      resolve-from: 5.0.0
    transitivePeerDependencies:
      - encoding
      - rollup
      - supports-color

  '@vinejs/compiler@3.0.0':
    optional: true

  '@vinejs/vine@3.0.1':
    dependencies:
      '@poppinss/macroable': 1.0.5
      '@types/validator': 13.15.2
      '@vinejs/compiler': 3.0.0
      camelcase: 8.0.0
      dayjs: 1.11.13
      dlv: 1.1.3
      normalize-url: 8.0.2
      validator: 13.15.15
    optional: true

  '@vitest/browser@3.2.4(playwright@1.53.2)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))(vitest@3.2.4)':
    dependencies:
      '@testing-library/dom': 10.4.0
      '@testing-library/user-event': 14.6.1(@testing-library/dom@10.4.0)
      '@vitest/mocker': 3.2.4(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      '@vitest/utils': 3.2.4
      magic-string: 0.30.17
      sirv: 3.0.1
      tinyrainbow: 2.0.0
      vitest: 3.2.4(@types/node@24.0.12)(@vitest/browser@3.2.4)(jiti@2.4.2)(lightningcss@1.30.1)
      ws: 8.18.3
    optionalDependencies:
      playwright: 1.53.2
    transitivePeerDependencies:
      - bufferutil
      - msw
      - utf-8-validate
      - vite

  '@vitest/coverage-v8@3.2.4(@vitest/browser@3.2.4)(vitest@3.2.4)':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@bcoe/v8-coverage': 1.0.2
      ast-v8-to-istanbul: 0.3.3
      debug: 4.4.1
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 5.0.6
      istanbul-reports: 3.1.7
      magic-string: 0.30.17
      magicast: 0.3.5
      std-env: 3.9.0
      test-exclude: 7.0.1
      tinyrainbow: 2.0.0
      vitest: 3.2.4(@types/node@24.0.12)(@vitest/browser@3.2.4)(jiti@2.4.2)(lightningcss@1.30.1)
    optionalDependencies:
      '@vitest/browser': 3.2.4(playwright@1.53.2)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))(vitest@3.2.4)
    transitivePeerDependencies:
      - supports-color

  '@vitest/expect@3.0.9':
    dependencies:
      '@vitest/spy': 3.0.9
      '@vitest/utils': 3.0.9
      chai: 5.2.1
      tinyrainbow: 2.0.0

  '@vitest/expect@3.2.4':
    dependencies:
      '@types/chai': 5.2.2
      '@vitest/spy': 3.2.4
      '@vitest/utils': 3.2.4
      chai: 5.2.1
      tinyrainbow: 2.0.0

  '@vitest/mocker@3.2.4(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))':
    dependencies:
      '@vitest/spy': 3.2.4
      estree-walker: 3.0.3
      magic-string: 0.30.17
    optionalDependencies:
      vite: 6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)

  '@vitest/mocker@3.2.4(vite@7.0.3(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))':
    dependencies:
      '@vitest/spy': 3.2.4
      estree-walker: 3.0.3
      magic-string: 0.30.17
    optionalDependencies:
      vite: 7.0.3(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)

  '@vitest/pretty-format@3.0.9':
    dependencies:
      tinyrainbow: 2.0.0

  '@vitest/pretty-format@3.2.4':
    dependencies:
      tinyrainbow: 2.0.0

  '@vitest/runner@3.2.4':
    dependencies:
      '@vitest/utils': 3.2.4
      pathe: 2.0.3
      strip-literal: 3.0.0

  '@vitest/snapshot@3.2.4':
    dependencies:
      '@vitest/pretty-format': 3.2.4
      magic-string: 0.30.17
      pathe: 2.0.3

  '@vitest/spy@3.0.9':
    dependencies:
      tinyspy: 3.0.2

  '@vitest/spy@3.2.4':
    dependencies:
      tinyspy: 4.0.3

  '@vitest/utils@3.0.9':
    dependencies:
      '@vitest/pretty-format': 3.0.9
      loupe: 3.1.4
      tinyrainbow: 2.0.0

  '@vitest/utils@3.2.4':
    dependencies:
      '@vitest/pretty-format': 3.2.4
      loupe: 3.1.4
      tinyrainbow: 2.0.0

  abbrev@3.0.1: {}

  acorn-import-attributes@1.9.5(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn-jsx@5.3.2(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn@8.15.0: {}

  agent-base@6.0.2:
    dependencies:
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  agent-base@7.1.4: {}

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.1: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  argparse@2.0.1: {}

  aria-query@5.3.0:
    dependencies:
      dequal: 2.0.3

  aria-query@5.3.2: {}

  arktype@2.1.20:
    dependencies:
      '@ark/schema': 0.46.0
      '@ark/util': 0.46.0
    optional: true

  assertion-error@2.0.1: {}

  ast-types@0.16.1:
    dependencies:
      tslib: 2.8.1

  ast-v8-to-istanbul@0.3.3:
    dependencies:
      '@jridgewell/trace-mapping': 0.3.29
      estree-walker: 3.0.3
      js-tokens: 9.0.1

  async-sema@3.1.1: {}

  axobject-query@4.1.0: {}

  balanced-match@1.0.2: {}

  better-opn@3.0.2:
    dependencies:
      open: 8.4.2

  big-integer@1.6.52: {}

  bin-links@5.0.0:
    dependencies:
      cmd-shim: 7.0.0
      npm-normalize-package-bin: 4.0.0
      proc-log: 5.0.0
      read-cmd-shim: 5.0.0
      write-file-atomic: 6.0.0

  binary-extensions@2.3.0: {}

  bindings@1.5.0:
    dependencies:
      file-uri-to-path: 1.0.0

  bits-ui@2.8.10(@internationalized/date@3.8.2)(svelte@5.35.4):
    dependencies:
      '@floating-ui/core': 1.7.2
      '@floating-ui/dom': 1.7.2
      '@internationalized/date': 3.8.2
      esm-env: 1.2.2
      runed: 0.29.2(svelte@5.35.4)
      svelte: 5.35.4
      svelte-toolbelt: 0.9.3(svelte@5.35.4)
      tabbable: 6.2.0

  brace-expansion@1.1.12:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.2:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.25.1:
    dependencies:
      caniuse-lite: 1.0.30001727
      electron-to-chromium: 1.5.180
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.25.1)

  buffer-from@1.1.2:
    optional: true

  cac@6.7.14: {}

  callsites@3.1.0: {}

  camelcase@8.0.0:
    optional: true

  caniuse-lite@1.0.30001727: {}

  chai@5.2.1:
    dependencies:
      assertion-error: 2.0.1
      check-error: 2.1.1
      deep-eql: 5.0.2
      loupe: 3.1.4
      pathval: 2.0.1

  chalk@3.0.0:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  check-error@2.1.1: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chokidar@4.0.3:
    dependencies:
      readdirp: 4.1.2

  chownr@3.0.0: {}

  chromatic@12.2.0: {}

  cjs-module-lexer@1.4.3: {}

  class-validator@0.14.2:
    dependencies:
      '@types/validator': 13.15.2
      libphonenumber-js: 1.12.9
      validator: 13.15.15
    optional: true

  clsx@2.1.1: {}

  cmd-shim@7.0.0: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  commander@2.20.3: {}

  commander@7.2.0: {}

  concat-map@0.0.1: {}

  consola@3.4.2: {}

  convert-source-map@2.0.0: {}

  cookie@0.6.0: {}

  cookie@1.0.2: {}

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  css.escape@1.5.1: {}

  cssesc@3.0.0: {}

  csstype@3.1.3: {}

  culori@4.0.2: {}

  d3-array@2.12.1:
    dependencies:
      internmap: 1.0.1

  d3-array@3.2.4:
    dependencies:
      internmap: 2.0.3

  d3-color@3.1.0: {}

  d3-delaunay@6.0.4:
    dependencies:
      delaunator: 5.0.1

  d3-dispatch@3.0.1: {}

  d3-dsv@3.0.1:
    dependencies:
      commander: 7.2.0
      iconv-lite: 0.6.3
      rw: 1.3.3

  d3-force@3.0.0:
    dependencies:
      d3-dispatch: 3.0.1
      d3-quadtree: 3.0.1
      d3-timer: 3.0.1

  d3-format@3.1.0: {}

  d3-geo-voronoi@2.1.0:
    dependencies:
      d3-array: 3.2.4
      d3-delaunay: 6.0.4
      d3-geo: 3.1.1
      d3-tricontour: 1.0.2

  d3-geo@3.1.1:
    dependencies:
      d3-array: 3.2.4

  d3-hierarchy@3.1.2: {}

  d3-interpolate-path@2.3.0: {}

  d3-interpolate@3.0.1:
    dependencies:
      d3-color: 3.1.0

  d3-path@1.0.9: {}

  d3-path@3.1.0: {}

  d3-quadtree@3.0.1: {}

  d3-random@3.0.1: {}

  d3-sankey@0.12.3:
    dependencies:
      d3-array: 2.12.1
      d3-shape: 1.3.7

  d3-scale-chromatic@3.1.0:
    dependencies:
      d3-color: 3.1.0
      d3-interpolate: 3.0.1

  d3-scale@4.0.2:
    dependencies:
      d3-array: 3.2.4
      d3-format: 3.1.0
      d3-interpolate: 3.0.1
      d3-time: 3.1.0
      d3-time-format: 4.1.0

  d3-shape@1.3.7:
    dependencies:
      d3-path: 1.0.9

  d3-shape@3.2.0:
    dependencies:
      d3-path: 3.1.0

  d3-tile@1.0.0: {}

  d3-time-format@4.1.0:
    dependencies:
      d3-time: 3.1.0

  d3-time@3.1.0:
    dependencies:
      d3-array: 3.2.4

  d3-timer@3.0.1: {}

  d3-tricontour@1.0.2:
    dependencies:
      d3-delaunay: 6.0.4
      d3-scale: 4.0.2

  data-uri-to-buffer@4.0.1: {}

  date-fns@4.1.0: {}

  dayjs@1.11.13:
    optional: true

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  dedent-js@1.0.1: {}

  dedent@1.6.0: {}

  deep-eql@5.0.2: {}

  deep-is@0.1.4: {}

  deepmerge@4.3.1: {}

  define-lazy-prop@2.0.0: {}

  delaunator@5.0.1:
    dependencies:
      robust-predicates: 3.0.2

  dequal@2.0.3: {}

  detect-libc@2.0.4: {}

  devalue@5.1.1: {}

  discontinuous-range@1.0.0: {}

  dlv@1.1.3:
    optional: true

  dom-accessibility-api@0.5.16: {}

  dom-accessibility-api@0.6.3: {}

  dom-serializer@2.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0

  domelementtype@2.3.0: {}

  domhandler@5.0.3:
    dependencies:
      domelementtype: 2.3.0

  domutils@3.2.2:
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3

  dotenv@16.6.1: {}

  eastasianwidth@0.2.0: {}

  effect@3.16.12:
    dependencies:
      '@standard-schema/spec': 1.0.0
      fast-check: 3.23.2
    optional: true

  electron-to-chromium@1.5.180: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  enhanced-resolve@5.18.2:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.2

  entities@4.5.0: {}

  es-module-lexer@1.7.0: {}

  es-toolkit@1.39.7: {}

  esbuild-register@3.6.0(esbuild@0.25.6):
    dependencies:
      debug: 4.4.1
      esbuild: 0.25.6
    transitivePeerDependencies:
      - supports-color

  esbuild-runner@2.2.2(esbuild@0.25.6):
    dependencies:
      esbuild: 0.25.6
      source-map-support: 0.5.21
      tslib: 2.4.0
    optional: true

  esbuild@0.25.6:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.6
      '@esbuild/android-arm': 0.25.6
      '@esbuild/android-arm64': 0.25.6
      '@esbuild/android-x64': 0.25.6
      '@esbuild/darwin-arm64': 0.25.6
      '@esbuild/darwin-x64': 0.25.6
      '@esbuild/freebsd-arm64': 0.25.6
      '@esbuild/freebsd-x64': 0.25.6
      '@esbuild/linux-arm': 0.25.6
      '@esbuild/linux-arm64': 0.25.6
      '@esbuild/linux-ia32': 0.25.6
      '@esbuild/linux-loong64': 0.25.6
      '@esbuild/linux-mips64el': 0.25.6
      '@esbuild/linux-ppc64': 0.25.6
      '@esbuild/linux-riscv64': 0.25.6
      '@esbuild/linux-s390x': 0.25.6
      '@esbuild/linux-x64': 0.25.6
      '@esbuild/netbsd-arm64': 0.25.6
      '@esbuild/netbsd-x64': 0.25.6
      '@esbuild/openbsd-arm64': 0.25.6
      '@esbuild/openbsd-x64': 0.25.6
      '@esbuild/openharmony-arm64': 0.25.6
      '@esbuild/sunos-x64': 0.25.6
      '@esbuild/win32-arm64': 0.25.6
      '@esbuild/win32-ia32': 0.25.6
      '@esbuild/win32-x64': 0.25.6

  escalade@3.2.0: {}

  escape-string-regexp@4.0.0: {}

  eslint-config-prettier@10.1.5(eslint@9.30.1(jiti@2.4.2)):
    dependencies:
      eslint: 9.30.1(jiti@2.4.2)

  eslint-plugin-storybook@9.1.0-alpha.2(eslint@9.30.1(jiti@2.4.2))(storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2))(typescript@5.8.3):
    dependencies:
      '@typescript-eslint/utils': 8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3)
      eslint: 9.30.1(jiti@2.4.2)
      storybook: 9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2)
    transitivePeerDependencies:
      - supports-color
      - typescript

  eslint-plugin-svelte@3.10.1(eslint@9.30.1(jiti@2.4.2))(svelte@5.35.4):
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.30.1(jiti@2.4.2))
      '@jridgewell/sourcemap-codec': 1.5.4
      eslint: 9.30.1(jiti@2.4.2)
      esutils: 2.0.3
      globals: 16.3.0
      known-css-properties: 0.37.0
      postcss: 8.5.6
      postcss-load-config: 3.1.4(postcss@8.5.6)
      postcss-safe-parser: 7.0.1(postcss@8.5.6)
      semver: 7.7.2
      svelte-eslint-parser: 1.2.0(svelte@5.35.4)
    optionalDependencies:
      svelte: 5.35.4
    transitivePeerDependencies:
      - ts-node

  eslint-scope@8.4.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.1: {}

  eslint@9.30.1(jiti@2.4.2):
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.30.1(jiti@2.4.2))
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.21.0
      '@eslint/config-helpers': 0.3.0
      '@eslint/core': 0.14.0
      '@eslint/eslintrc': 3.3.1
      '@eslint/js': 9.30.1
      '@eslint/plugin-kit': 0.3.3
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.3
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      escape-string-regexp: 4.0.0
      eslint-scope: 8.4.0
      eslint-visitor-keys: 4.2.1
      espree: 10.4.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    optionalDependencies:
      jiti: 2.4.2
    transitivePeerDependencies:
      - supports-color

  esm-env@1.2.2: {}

  espree@10.4.0:
    dependencies:
      acorn: 8.15.0
      acorn-jsx: 5.3.2(acorn@8.15.0)
      eslint-visitor-keys: 4.2.1

  esprima@4.0.1: {}

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrap@1.2.2:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4
      '@types/estree': 1.0.8

  esrap@1.4.9:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4

  esrap@2.1.0:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@5.3.0: {}

  estree-walker@2.0.2: {}

  estree-walker@3.0.3:
    dependencies:
      '@types/estree': 1.0.8

  esutils@2.0.3: {}

  expect-type@1.2.2: {}

  fast-check@3.23.2:
    dependencies:
      pure-rand: 6.1.0
    optional: true

  fast-deep-equal@2.0.1: {}

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fdir@6.4.6(picomatch@4.0.2):
    optionalDependencies:
      picomatch: 4.0.2

  fetch-blob@3.2.0:
    dependencies:
      node-domexception: 1.0.0
      web-streams-polyfill: 3.3.3

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  file-uri-to-path@1.0.0: {}

  filesize@10.1.6: {}

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4

  flatted@3.3.3: {}

  foreground-child@3.3.1:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  formdata-polyfill@4.0.10:
    dependencies:
      fetch-blob: 3.2.0

  formsnap@2.0.1(svelte@5.35.4)(sveltekit-superforms@2.27.1(@sveltejs/kit@2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(@types/json-schema@7.0.15)(esbuild@0.25.6)(svelte@5.35.4)(typescript@5.8.3)):
    dependencies:
      svelte: 5.35.4
      svelte-toolbelt: 0.5.0(svelte@5.35.4)
      sveltekit-superforms: 2.27.1(@sveltejs/kit@2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(@types/json-schema@7.0.15)(esbuild@0.25.6)(svelte@5.35.4)(typescript@5.8.3)

  forwarded-parse@2.1.2: {}

  fs.realpath@1.0.0: {}

  fsevents@2.3.2:
    optional: true

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  gensync@1.0.0-beta.2: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1

  glob@9.3.5:
    dependencies:
      fs.realpath: 1.0.0
      minimatch: 8.0.4
      minipass: 4.2.8
      path-scurry: 1.11.1

  globals@14.0.0: {}

  globals@16.3.0: {}

  globalyzer@0.1.0: {}

  globrex@0.1.2: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  has-flag@4.0.0: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  html-escaper@2.0.2: {}

  html-to-text@9.0.5:
    dependencies:
      '@selderee/plugin-htmlparser2': 0.11.0
      deepmerge: 4.3.1
      dom-serializer: 2.0.0
      htmlparser2: 8.0.2
      selderee: 0.11.0

  htmlparser2@8.0.2:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      domutils: 3.2.2
      entities: 4.5.0

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@7.0.6:
    dependencies:
      agent-base: 7.1.4
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  ignore@5.3.2: {}

  ignore@7.0.5: {}

  immer@10.1.1: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-in-the-middle@1.14.2:
    dependencies:
      acorn: 8.15.0
      acorn-import-attributes: 1.9.5(acorn@8.15.0)
      cjs-module-lexer: 1.4.3
      module-details-from-path: 1.0.4

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  inline-style-parser@0.2.4: {}

  internmap@1.0.1: {}

  internmap@2.0.3: {}

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-docker@2.2.1: {}

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-number@7.0.0: {}

  is-reference@3.0.3:
    dependencies:
      '@types/estree': 1.0.8

  is-wsl@2.2.0:
    dependencies:
      is-docker: 2.2.1

  isexe@2.0.0: {}

  isows@1.0.7(ws@8.18.3):
    dependencies:
      ws: 8.18.3

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-lib-source-maps@5.0.6:
    dependencies:
      '@jridgewell/trace-mapping': 0.3.29
      debug: 4.4.1
      istanbul-lib-coverage: 3.2.2
    transitivePeerDependencies:
      - supports-color

  istanbul-reports@3.1.7:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  jackspeak@3.4.3:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jiti@2.4.2: {}

  joi@17.13.3:
    dependencies:
      '@hapi/hoek': 9.3.0
      '@hapi/topo': 5.1.0
      '@sideway/address': 4.1.5
      '@sideway/formula': 3.0.1
      '@sideway/pinpoint': 2.0.0
    optional: true

  js-tokens@4.0.0: {}

  js-tokens@9.0.1: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-schema-to-ts@3.1.1:
    dependencies:
      '@babel/runtime': 7.27.6
      ts-algebra: 2.0.0
    optional: true

  json-schema-traverse@0.4.1: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@2.2.3: {}

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jsox@1.2.123: {}

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kleur@3.0.3: {}

  kleur@4.1.5: {}

  known-css-properties@0.37.0: {}

  layerchart@2.0.0-next.6(svelte@5.35.4):
    dependencies:
      '@dagrejs/dagre': 1.1.5
      '@layerstack/svelte-actions': 1.0.1-next.2
      '@layerstack/svelte-state': 0.1.0-next.3
      '@layerstack/svelte-stores': 1.0.2-next.2
      '@layerstack/tailwind': 2.0.0-next.4
      '@layerstack/utils': 1.1.0-next.2
      d3-array: 3.2.4
      d3-color: 3.1.0
      d3-delaunay: 6.0.4
      d3-dsv: 3.0.1
      d3-force: 3.0.0
      d3-geo: 3.1.1
      d3-geo-voronoi: 2.1.0
      d3-hierarchy: 3.1.2
      d3-interpolate: 3.0.1
      d3-interpolate-path: 2.3.0
      d3-path: 3.1.0
      d3-quadtree: 3.0.1
      d3-random: 3.0.1
      d3-sankey: 0.12.3
      d3-scale: 4.0.2
      d3-scale-chromatic: 3.1.0
      d3-shape: 3.2.0
      d3-tile: 1.0.0
      d3-time: 3.1.0
      date-fns: 4.1.0
      lodash-es: 4.17.21
      runed: 0.27.0(svelte@5.35.4)
      svelte: 5.35.4

  leac@0.6.0: {}

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  libphonenumber-js@1.12.9:
    optional: true

  lightningcss-darwin-arm64@1.30.1:
    optional: true

  lightningcss-darwin-x64@1.30.1:
    optional: true

  lightningcss-freebsd-x64@1.30.1:
    optional: true

  lightningcss-linux-arm-gnueabihf@1.30.1:
    optional: true

  lightningcss-linux-arm64-gnu@1.30.1:
    optional: true

  lightningcss-linux-arm64-musl@1.30.1:
    optional: true

  lightningcss-linux-x64-gnu@1.30.1:
    optional: true

  lightningcss-linux-x64-musl@1.30.1:
    optional: true

  lightningcss-win32-arm64-msvc@1.30.1:
    optional: true

  lightningcss-win32-x64-msvc@1.30.1:
    optional: true

  lightningcss@1.30.1:
    dependencies:
      detect-libc: 2.0.4
    optionalDependencies:
      lightningcss-darwin-arm64: 1.30.1
      lightningcss-darwin-x64: 1.30.1
      lightningcss-freebsd-x64: 1.30.1
      lightningcss-linux-arm-gnueabihf: 1.30.1
      lightningcss-linux-arm64-gnu: 1.30.1
      lightningcss-linux-arm64-musl: 1.30.1
      lightningcss-linux-x64-gnu: 1.30.1
      lightningcss-linux-x64-musl: 1.30.1
      lightningcss-win32-arm64-msvc: 1.30.1
      lightningcss-win32-x64-msvc: 1.30.1

  lilconfig@2.1.0: {}

  locate-character@3.0.0: {}

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash-es@4.17.21: {}

  lodash.castarray@4.4.0: {}

  lodash.isplainobject@4.0.6: {}

  lodash.merge@4.6.2: {}

  lodash@4.17.21: {}

  loupe@3.1.4: {}

  lower-case@2.0.2:
    dependencies:
      tslib: 2.8.1

  lru-cache@10.4.3: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lz-string@1.5.0: {}

  magic-string@0.30.17:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4

  magic-string@0.30.7:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4

  magic-string@0.30.8:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4

  magicast@0.3.5:
    dependencies:
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.0
      source-map-js: 1.2.1

  make-dir@4.0.0:
    dependencies:
      semver: 7.7.2

  memoize-weak@1.0.2: {}

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  min-indent@1.0.1: {}

  mini-svg-data-uri@1.4.4: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.12

  minimatch@8.0.4:
    dependencies:
      brace-expansion: 2.0.2

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.2

  minimist@1.2.8: {}

  minipass@4.2.8: {}

  minipass@7.1.2: {}

  minizlib@3.0.2:
    dependencies:
      minipass: 7.1.2

  mkdirp@3.0.1: {}

  mode-watcher@1.1.0(svelte@5.35.4):
    dependencies:
      runed: 0.25.0(svelte@5.35.4)
      svelte: 5.35.4
      svelte-toolbelt: 0.7.1(svelte@5.35.4)

  module-details-from-path@1.0.4: {}

  moo@0.5.2: {}

  mri@1.2.0: {}

  mrmime@2.0.1: {}

  ms@2.1.3: {}

  nanoid@3.3.11: {}

  natural-compare@1.4.0: {}

  nearley@2.20.1:
    dependencies:
      commander: 2.20.3
      moo: 0.5.2
      railroad-diagrams: 1.0.0
      randexp: 0.4.6

  no-case@3.0.4:
    dependencies:
      lower-case: 2.0.2
      tslib: 2.8.1

  node-domexception@1.0.0: {}

  node-fetch@2.7.0:
    dependencies:
      whatwg-url: 5.0.0

  node-fetch@3.3.2:
    dependencies:
      data-uri-to-buffer: 4.0.1
      fetch-blob: 3.2.0
      formdata-polyfill: 4.0.10

  node-gyp-build@4.8.4: {}

  node-releases@2.0.19: {}

  node-sql-parser@5.3.10:
    dependencies:
      '@types/pegjs': 0.10.6
      big-integer: 1.6.52

  nopt@8.1.0:
    dependencies:
      abbrev: 3.0.1

  normalize-path@3.0.0: {}

  normalize-url@8.0.2:
    optional: true

  npm-normalize-package-bin@4.0.0: {}

  open@8.4.2:
    dependencies:
      define-lazy-prop: 2.0.0
      is-docker: 2.2.1
      is-wsl: 2.2.0

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  package-json-from-dist@1.0.1: {}

  paneforge@1.0.0(svelte@5.35.4):
    dependencies:
      runed: 0.23.4(svelte@5.35.4)
      svelte: 5.35.4
      svelte-toolbelt: 0.9.3(svelte@5.35.4)

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parseley@0.12.1:
    dependencies:
      leac: 0.6.0
      peberminta: 0.9.0

  pascal-case@3.1.2:
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1

  path-exists@4.0.0: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  pathe@2.0.3: {}

  pathval@2.0.1: {}

  peberminta@0.9.0: {}

  pg-int8@1.0.1: {}

  pg-protocol@1.10.3: {}

  pg-types@2.2.0:
    dependencies:
      pg-int8: 1.0.1
      postgres-array: 2.0.0
      postgres-bytea: 1.0.0
      postgres-date: 1.0.7
      postgres-interval: 1.2.0

  phosphor-svelte@3.0.1(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)):
    dependencies:
      estree-walker: 3.0.3
      magic-string: 0.30.17
      svelte: 5.35.4
    optionalDependencies:
      vite: 6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  playwright-core@1.53.2: {}

  playwright@1.53.2:
    dependencies:
      playwright-core: 1.53.2
    optionalDependencies:
      fsevents: 2.3.2

  postcss-load-config@3.1.4(postcss@8.5.6):
    dependencies:
      lilconfig: 2.1.0
      yaml: 1.10.2
    optionalDependencies:
      postcss: 8.5.6

  postcss-safe-parser@7.0.1(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-scss@4.0.9(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-selector-parser@6.0.10:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-selector-parser@7.1.0:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss@8.5.6:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  postgres-array@2.0.0: {}

  postgres-bytea@1.0.0: {}

  postgres-date@1.0.7: {}

  postgres-interval@1.2.0:
    dependencies:
      xtend: 4.0.2

  prelude-ls@1.2.1: {}

  prettier-plugin-sql@0.19.1(prettier@3.6.2):
    dependencies:
      jsox: 1.2.123
      node-sql-parser: 5.3.10
      prettier: 3.6.2
      sql-formatter: 15.6.6
      tslib: 2.8.1

  prettier-plugin-svelte@3.4.0(prettier@3.6.2)(svelte@5.35.4):
    dependencies:
      prettier: 3.6.2
      svelte: 5.35.4

  prettier-plugin-tailwindcss@0.6.13(prettier-plugin-svelte@3.4.0(prettier@3.6.2)(svelte@5.35.4))(prettier@3.6.2):
    dependencies:
      prettier: 3.6.2
    optionalDependencies:
      prettier-plugin-svelte: 3.4.0(prettier@3.6.2)(svelte@5.35.4)

  prettier@3.6.2: {}

  pretty-format@27.5.1:
    dependencies:
      ansi-regex: 5.0.1
      ansi-styles: 5.2.0
      react-is: 17.0.2

  pretty-quick@4.2.2(prettier@3.6.2):
    dependencies:
      '@pkgr/core': 0.2.7
      ignore: 7.0.5
      mri: 1.2.0
      picocolors: 1.1.1
      picomatch: 4.0.2
      prettier: 3.6.2
      tinyexec: 0.3.2
      tslib: 2.8.1

  proc-log@5.0.0: {}

  progress@2.0.3: {}

  prompts@2.4.2:
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5

  property-expr@2.0.6:
    optional: true

  proxy-from-env@1.1.0: {}

  punycode@2.3.1: {}

  pure-rand@6.1.0:
    optional: true

  queue-microtask@1.2.3: {}

  railroad-diagrams@1.0.0: {}

  randexp@0.4.6:
    dependencies:
      discontinuous-range: 1.0.0
      ret: 0.1.15

  react-dom@19.1.0(react@19.1.0):
    dependencies:
      react: 19.1.0
      scheduler: 0.26.0

  react-is@17.0.2: {}

  react-promise-suspense@0.3.4:
    dependencies:
      fast-deep-equal: 2.0.1

  react@19.1.0: {}

  read-cmd-shim@5.0.0: {}

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  readdirp@4.1.2: {}

  recast@0.23.11:
    dependencies:
      ast-types: 0.16.1
      esprima: 4.0.1
      source-map: 0.6.1
      tiny-invariant: 1.3.3
      tslib: 2.8.1

  redent@3.0.0:
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0

  require-in-the-middle@7.5.2:
    dependencies:
      debug: 4.4.1
      module-details-from-path: 1.0.4
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  resend@4.6.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      '@react-email/render': 1.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
    transitivePeerDependencies:
      - react
      - react-dom

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  ret@0.1.15: {}

  reusify@1.1.0: {}

  robust-predicates@3.0.2: {}

  rollup@4.44.2:
    dependencies:
      '@types/estree': 1.0.8
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.44.2
      '@rollup/rollup-android-arm64': 4.44.2
      '@rollup/rollup-darwin-arm64': 4.44.2
      '@rollup/rollup-darwin-x64': 4.44.2
      '@rollup/rollup-freebsd-arm64': 4.44.2
      '@rollup/rollup-freebsd-x64': 4.44.2
      '@rollup/rollup-linux-arm-gnueabihf': 4.44.2
      '@rollup/rollup-linux-arm-musleabihf': 4.44.2
      '@rollup/rollup-linux-arm64-gnu': 4.44.2
      '@rollup/rollup-linux-arm64-musl': 4.44.2
      '@rollup/rollup-linux-loongarch64-gnu': 4.44.2
      '@rollup/rollup-linux-powerpc64le-gnu': 4.44.2
      '@rollup/rollup-linux-riscv64-gnu': 4.44.2
      '@rollup/rollup-linux-riscv64-musl': 4.44.2
      '@rollup/rollup-linux-s390x-gnu': 4.44.2
      '@rollup/rollup-linux-x64-gnu': 4.44.2
      '@rollup/rollup-linux-x64-musl': 4.44.2
      '@rollup/rollup-win32-arm64-msvc': 4.44.2
      '@rollup/rollup-win32-ia32-msvc': 4.44.2
      '@rollup/rollup-win32-x64-msvc': 4.44.2
      fsevents: 2.3.3

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  runed@0.23.4(svelte@5.35.4):
    dependencies:
      esm-env: 1.2.2
      svelte: 5.35.4

  runed@0.25.0(svelte@5.35.4):
    dependencies:
      esm-env: 1.2.2
      svelte: 5.35.4

  runed@0.27.0(svelte@5.35.4):
    dependencies:
      esm-env: 1.2.2
      svelte: 5.35.4

  runed@0.28.0(svelte@5.35.4):
    dependencies:
      esm-env: 1.2.2
      svelte: 5.35.4

  runed@0.29.2(svelte@5.35.4):
    dependencies:
      esm-env: 1.2.2
      svelte: 5.35.4

  rw@1.3.3: {}

  sade@1.8.1:
    dependencies:
      mri: 1.2.0

  safer-buffer@2.1.2: {}

  scheduler@0.26.0: {}

  selderee@0.11.0:
    dependencies:
      parseley: 0.12.1

  semver@6.3.1: {}

  semver@7.7.2: {}

  set-cookie-parser@2.7.1: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shimmer@1.2.1: {}

  siginfo@2.0.0: {}

  signal-exit@4.1.0: {}

  simple-git-hooks@2.13.0: {}

  sirv@3.0.1:
    dependencies:
      '@polka/url': 1.0.0-next.29
      mrmime: 2.0.1
      totalist: 3.0.1

  sisteransi@1.0.5: {}

  sorcery@1.0.0:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4
      minimist: 1.2.8
      tiny-glob: 0.2.9

  source-map-js@1.2.1: {}

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1
    optional: true

  source-map@0.6.1: {}

  sql-formatter@15.6.6:
    dependencies:
      argparse: 2.0.1
      nearley: 2.20.1

  stackback@0.0.2: {}

  std-env@3.9.0: {}

  storybook@9.1.0-alpha.2(@testing-library/dom@10.4.0)(prettier@3.6.2):
    dependencies:
      '@storybook/global': 5.0.0
      '@testing-library/jest-dom': 6.6.3
      '@testing-library/user-event': 14.6.1(@testing-library/dom@10.4.0)
      '@vitest/expect': 3.0.9
      '@vitest/spy': 3.0.9
      better-opn: 3.0.2
      esbuild: 0.25.6
      esbuild-register: 3.6.0(esbuild@0.25.6)
      recast: 0.23.11
      semver: 7.7.2
      ws: 8.18.3
    optionalDependencies:
      prettier: 3.6.2
    transitivePeerDependencies:
      - '@testing-library/dom'
      - bufferutil
      - supports-color
      - utf-8-validate

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-indent@3.0.0:
    dependencies:
      min-indent: 1.0.1

  strip-json-comments@3.1.1: {}

  strip-literal@3.0.0:
    dependencies:
      js-tokens: 9.0.1

  style-to-object@1.0.9:
    dependencies:
      inline-style-parser: 0.2.4

  supabase@2.30.4:
    dependencies:
      bin-links: 5.0.0
      https-proxy-agent: 7.0.6
      node-fetch: 3.3.2
      tar: 7.4.3
    transitivePeerDependencies:
      - supports-color

  superstruct@2.0.2:
    optional: true

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  svelte-ast-print@0.4.2(svelte@5.35.4):
    dependencies:
      esrap: 1.2.2
      svelte: 5.35.4
      zimmerframe: 1.1.2

  svelte-check@4.2.2(picomatch@4.0.2)(svelte@5.35.4)(typescript@5.8.3):
    dependencies:
      '@jridgewell/trace-mapping': 0.3.29
      chokidar: 4.0.3
      fdir: 6.4.6(picomatch@4.0.2)
      picocolors: 1.1.1
      sade: 1.8.1
      svelte: 5.35.4
      typescript: 5.8.3
    transitivePeerDependencies:
      - picomatch

  svelte-eslint-parser@1.2.0(svelte@5.35.4):
    dependencies:
      eslint-scope: 8.4.0
      eslint-visitor-keys: 4.2.1
      espree: 10.4.0
      postcss: 8.5.6
      postcss-scss: 4.0.9(postcss@8.5.6)
      postcss-selector-parser: 7.1.0
    optionalDependencies:
      svelte: 5.35.4

  svelte-sonner@1.0.5(svelte@5.35.4):
    dependencies:
      runed: 0.28.0(svelte@5.35.4)
      svelte: 5.35.4

  svelte-toolbelt@0.5.0(svelte@5.35.4):
    dependencies:
      clsx: 2.1.1
      style-to-object: 1.0.9
      svelte: 5.35.4

  svelte-toolbelt@0.7.1(svelte@5.35.4):
    dependencies:
      clsx: 2.1.1
      runed: 0.23.4(svelte@5.35.4)
      style-to-object: 1.0.9
      svelte: 5.35.4

  svelte-toolbelt@0.9.3(svelte@5.35.4):
    dependencies:
      clsx: 2.1.1
      runed: 0.29.2(svelte@5.35.4)
      style-to-object: 1.0.9
      svelte: 5.35.4

  svelte2tsx@0.7.40(svelte@5.35.4)(typescript@5.8.3):
    dependencies:
      dedent-js: 1.0.1
      pascal-case: 3.1.2
      svelte: 5.35.4
      typescript: 5.8.3

  svelte@5.35.4:
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@jridgewell/sourcemap-codec': 1.5.4
      '@sveltejs/acorn-typescript': 1.0.5(acorn@8.15.0)
      '@types/estree': 1.0.8
      acorn: 8.15.0
      aria-query: 5.3.2
      axobject-query: 4.1.0
      clsx: 2.1.1
      esm-env: 1.2.2
      esrap: 2.1.0
      is-reference: 3.0.3
      locate-character: 3.0.0
      magic-string: 0.30.17
      zimmerframe: 1.1.2

  sveltekit-flash-message@2.4.6(@sveltejs/kit@2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4):
    dependencies:
      '@sveltejs/kit': 2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      svelte: 5.35.4

  sveltekit-superforms@2.27.1(@sveltejs/kit@2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(@types/json-schema@7.0.15)(esbuild@0.25.6)(svelte@5.35.4)(typescript@5.8.3):
    dependencies:
      '@sveltejs/kit': 2.22.3(@sveltejs/vite-plugin-svelte@5.1.0(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)))(svelte@5.35.4)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      devalue: 5.1.1
      memoize-weak: 1.0.2
      svelte: 5.35.4
      ts-deepmerge: 7.0.3
    optionalDependencies:
      '@exodus/schemasafe': 1.3.0
      '@gcornut/valibot-json-schema': 0.42.0(esbuild@0.25.6)(typescript@5.8.3)
      '@sinclair/typebox': 0.34.37
      '@typeschema/class-validator': 0.3.0(@types/json-schema@7.0.15)(class-validator@0.14.2)
      '@vinejs/vine': 3.0.1
      arktype: 2.1.20
      class-validator: 0.14.2
      effect: 3.16.12
      joi: 17.13.3
      json-schema-to-ts: 3.1.1
      superstruct: 2.0.2
      valibot: 1.1.0(typescript@5.8.3)
      yup: 1.6.1
      zod: 3.25.76
      zod-to-json-schema: 3.24.6(zod@3.25.76)
    transitivePeerDependencies:
      - '@types/json-schema'
      - esbuild
      - typescript

  tabbable@6.2.0: {}

  tailwind-merge@3.0.2: {}

  tailwind-merge@3.3.1: {}

  tailwind-variants@1.0.0(tailwindcss@4.1.11):
    dependencies:
      tailwind-merge: 3.0.2
      tailwindcss: 4.1.11

  tailwindcss@4.1.11: {}

  tapable@2.2.2: {}

  tar@7.4.3:
    dependencies:
      '@isaacs/fs-minipass': 4.0.1
      chownr: 3.0.0
      minipass: 7.1.2
      minizlib: 3.0.2
      mkdirp: 3.0.1
      yallist: 5.0.0

  test-exclude@7.0.1:
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 10.4.5
      minimatch: 9.0.5

  tiny-case@1.0.3:
    optional: true

  tiny-glob@0.2.9:
    dependencies:
      globalyzer: 0.1.0
      globrex: 0.1.2

  tiny-invariant@1.3.3: {}

  tinybench@2.9.0: {}

  tinyexec@0.3.2: {}

  tinyglobby@0.2.14:
    dependencies:
      fdir: 6.4.6(picomatch@4.0.2)
      picomatch: 4.0.2

  tinypool@1.1.1: {}

  tinyrainbow@2.0.0: {}

  tinyspy@3.0.2: {}

  tinyspy@4.0.3: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toposort@2.0.2:
    optional: true

  totalist@3.0.1: {}

  tr46@0.0.3: {}

  ts-algebra@2.0.0:
    optional: true

  ts-api-utils@2.1.0(typescript@5.8.3):
    dependencies:
      typescript: 5.8.3

  ts-dedent@2.2.0: {}

  ts-deepmerge@7.0.3: {}

  tslib@2.4.0:
    optional: true

  tslib@2.8.1: {}

  tw-animate-css@1.3.5: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@2.19.0: {}

  typescript-eslint@8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3):
    dependencies:
      '@typescript-eslint/eslint-plugin': 8.36.0(@typescript-eslint/parser@8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3))(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/parser': 8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/utils': 8.36.0(eslint@9.30.1(jiti@2.4.2))(typescript@5.8.3)
      eslint: 9.30.1(jiti@2.4.2)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  typescript@5.8.3: {}

  uid@2.0.2:
    dependencies:
      '@lukeed/csprng': 1.1.0

  undici-types@7.8.0: {}

  universalify@2.0.1: {}

  unplugin@1.0.1:
    dependencies:
      acorn: 8.15.0
      chokidar: 3.6.0
      webpack-sources: 3.3.3
      webpack-virtual-modules: 0.5.0

  unplugin@1.16.1:
    dependencies:
      acorn: 8.15.0
      webpack-virtual-modules: 0.6.2

  update-browserslist-db@1.1.3(browserslist@4.25.1):
    dependencies:
      browserslist: 4.25.1
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  util-deprecate@1.0.2: {}

  valibot@0.42.1(typescript@5.8.3):
    optionalDependencies:
      typescript: 5.8.3
    optional: true

  valibot@1.1.0(typescript@5.8.3):
    optionalDependencies:
      typescript: 5.8.3
    optional: true

  validator@13.15.15:
    optional: true

  vite-node@3.2.4(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1):
    dependencies:
      cac: 6.7.14
      debug: 4.4.1
      es-module-lexer: 1.7.0
      pathe: 2.0.3
      vite: 7.0.3(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)
    transitivePeerDependencies:
      - '@types/node'
      - jiti
      - less
      - lightningcss
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - yaml

  vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1):
    dependencies:
      esbuild: 0.25.6
      fdir: 6.4.6(picomatch@4.0.2)
      picomatch: 4.0.2
      postcss: 8.5.6
      rollup: 4.44.2
      tinyglobby: 0.2.14
    optionalDependencies:
      '@types/node': 24.0.12
      fsevents: 2.3.3
      jiti: 2.4.2
      lightningcss: 1.30.1

  vite@7.0.3(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1):
    dependencies:
      esbuild: 0.25.6
      fdir: 6.4.6(picomatch@4.0.2)
      picomatch: 4.0.2
      postcss: 8.5.6
      rollup: 4.44.2
      tinyglobby: 0.2.14
    optionalDependencies:
      '@types/node': 24.0.12
      fsevents: 2.3.3
      jiti: 2.4.2
      lightningcss: 1.30.1

  vitefu@1.1.1(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)):
    optionalDependencies:
      vite: 6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)

  vitest-browser-svelte@1.0.0(@vitest/browser@3.2.4)(svelte@5.35.4)(vitest@3.2.4):
    dependencies:
      '@vitest/browser': 3.2.4(playwright@1.53.2)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))(vitest@3.2.4)
      svelte: 5.35.4
      vitest: 3.2.4(@types/node@24.0.12)(@vitest/browser@3.2.4)(jiti@2.4.2)(lightningcss@1.30.1)

  vitest@3.2.4(@types/node@24.0.12)(@vitest/browser@3.2.4)(jiti@2.4.2)(lightningcss@1.30.1):
    dependencies:
      '@types/chai': 5.2.2
      '@vitest/expect': 3.2.4
      '@vitest/mocker': 3.2.4(vite@7.0.3(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))
      '@vitest/pretty-format': 3.2.4
      '@vitest/runner': 3.2.4
      '@vitest/snapshot': 3.2.4
      '@vitest/spy': 3.2.4
      '@vitest/utils': 3.2.4
      chai: 5.2.1
      debug: 4.4.1
      expect-type: 1.2.2
      magic-string: 0.30.17
      pathe: 2.0.3
      picomatch: 4.0.2
      std-env: 3.9.0
      tinybench: 2.9.0
      tinyexec: 0.3.2
      tinyglobby: 0.2.14
      tinypool: 1.1.1
      tinyrainbow: 2.0.0
      vite: 7.0.3(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)
      vite-node: 3.2.4(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1)
      why-is-node-running: 2.3.0
    optionalDependencies:
      '@types/node': 24.0.12
      '@vitest/browser': 3.2.4(playwright@1.53.2)(vite@6.3.5(@types/node@24.0.12)(jiti@2.4.2)(lightningcss@1.30.1))(vitest@3.2.4)
    transitivePeerDependencies:
      - jiti
      - less
      - lightningcss
      - msw
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - yaml

  web-streams-polyfill@3.3.3: {}

  webidl-conversions@3.0.1: {}

  webpack-sources@3.3.3: {}

  webpack-virtual-modules@0.5.0: {}

  webpack-virtual-modules@0.6.2: {}

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  why-is-node-running@2.3.0:
    dependencies:
      siginfo: 2.0.0
      stackback: 0.0.2

  word-wrap@1.2.5: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  write-file-atomic@6.0.0:
    dependencies:
      imurmurhash: 0.1.4
      signal-exit: 4.1.0

  ws@8.18.3: {}

  xlsx@file:vendor/xlsx-0.20.3.tgz: {}

  xtend@4.0.2: {}

  yallist@3.1.1: {}

  yallist@5.0.0: {}

  yaml@1.10.2: {}

  yocto-queue@0.1.0: {}

  yup@1.6.1:
    dependencies:
      property-expr: 2.0.6
      tiny-case: 1.0.3
      toposort: 2.0.2
      type-fest: 2.19.0
    optional: true

  zimmerframe@1.1.2: {}

  zod-to-json-schema@3.24.6(zod@3.25.76):
    dependencies:
      zod: 3.25.76
    optional: true

  zod@3.25.76: {}
