{"name": "project-controls", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "reset-db": "supabase db reset && supabase gen types typescript --local > src/lib/database.types.ts && pnpm format", "test:e2e": "playwright test", "test:mcp": "playwright test --config=playwright-mcp.config.ts", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test": "npm run test:unit -- --run && npm run test:e2e", "test:unit": "vitest run", "test:server": "vitest --project=server", "test:client": "vitest --project=client", "test:ssr": "vitest --project=ssr"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@eslint/compat": "^1.3.1", "@eslint/js": "^9.30.1", "@lucide/svelte": "^0.525.0", "@playwright/test": "^1.53.2", "@storybook/addon-docs": "9.1.0-alpha.2", "@storybook/addon-svelte-csf": "5.0.6", "@storybook/addon-vitest": "9.1.0-alpha.2", "@storybook/sveltekit": "9.1.0-alpha.2", "@sveltejs/adapter-vercel": "^5.7.2", "@sveltejs/kit": "^2.22.3", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@tanstack/table-core": "^8.21.3", "@types/d3-color": "^3.1.3", "@types/d3-hierarchy": "^3.1.7", "@types/d3-scale": "^4.0.9", "@types/d3-scale-chromatic": "^3.1.0", "@vitest/browser": "3.2.4", "@vitest/coverage-v8": "3.2.4", "bits-ui": "^2.8.10", "clsx": "^2.1.1", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-storybook": "9.1.0-alpha.2", "eslint-plugin-svelte": "^3.10.1", "formsnap": "^2.0.1", "globals": "^16.3.0", "layerchart": "2.0.0-next.6", "mode-watcher": "^1.1.0", "paneforge": "1.0.0", "phosphor-svelte": "^3.0.1", "playwright": "^1.53.2", "prettier": "^3.6.2", "prettier-plugin-sql": "^0.19.1", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.13", "pretty-quick": "^4.2.2", "simple-git-hooks": "^2.13.0", "storybook": "9.1.0-alpha.2", "supabase": "^2.30.4", "svelte": "^5.35.4", "svelte-check": "^4.2.2", "svelte-sonner": "^1.0.5", "sveltekit-flash-message": "^2.4.6", "sveltekit-superforms": "^2.27.1", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "vite": "^6.3.5", "vitest": "^3.2.4", "vitest-browser-svelte": "^1.0.0", "zod": "^3.25.76"}, "dependencies": {"@internationalized/date": "^3.8.2", "@sentry/sveltekit": "^9.36.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.3", "d3-color": "^3.1.0", "d3-hierarchy": "^3.1.2", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.1.0", "date-fns": "^4.1.0", "resend": "^4.6.0", "uid": "^2.0.2", "xlsx": "file:vendor/xlsx-0.20.3.tgz"}, "packageManager": "pnpm@10.11.0", "pnpm": {"onlyBuiltDependencies": ["@sentry/cli", "@tailwindcss/oxide", "esbuild", "msw", "simple-git-hooks", "supabase", "svelte-preprocess"], "ignoredBuiltDependencies": []}}